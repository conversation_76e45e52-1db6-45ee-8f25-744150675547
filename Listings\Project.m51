BL51 BANKED LINKER/LOCATER V6.22                                                        07/08/2025  00:30:49  PAGE 1


BL51 BANKED LINKER/LOCATER V6.22, INVOKED BY:
E:\KEIL\C51\BIN\BL51.EXE .\Objects\main.obj, .\Objects\ds18b20.obj, .\Objects\lcd9648.obj, .\Objects\Key.obj TO .\Object
>> s\Project PRINT (.\Listings\Project.m51) RAMSIZE (256)


MEMORY MODEL: SMALL WITH FLOATING POINT ARITHMETIC


INPUT MODULES INCLUDED:
  .\Objects\main.obj (MAIN)
  .\Objects\ds18b20.obj (DS18B20)
  .\Objects\lcd9648.obj (LCD9648)
  .\Objects\Key.obj (KEY)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?FPADD)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?FPMUL)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?FPCMP)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?FCAST)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?CASTF)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?FPGETOPN)
  E:\KEIL\C51\LIB\C51S.LIB (?C_STARTUP)
  E:\KEIL\C51\LIB\C51S.LIB (?C?CLDPTR)
  E:\KEIL\C51\LIB\C51S.LIB (?C?CLDOPTR)
  E:\KEIL\C51\LIB\C51S.LIB (?C?CSTPTR)
  E:\KEIL\C51\LIB\C51S.LIB (?C?CSTOPTR)
  E:\KEIL\C51\LIB\C51S.LIB (?C?LNEG)
  E:\KEIL\C51\LIB\C51S.LIB (?C?LLDIDATA)
  E:\KEIL\C51\LIB\C51S.LIB (?C?LSTIDATA)
  E:\KEIL\C51\LIB\C51S.LIB (?C?LSTKIDATA)
  E:\KEIL\C51\LIB\C51S.LIB (?C_INIT)


LINK MAP OF MODULE:  .\Objects\Project (MAIN)


            TYPE    BASE      LENGTH    RELOCATION   SEGMENT NAME
            -----------------------------------------------------

            * * * * * * *   D A T A   M E M O R Y   * * * * * * *
            REG     0000H     0008H     ABSOLUTE     "REG BANK 0"
            DATA    0008H     0013H     UNIT         _DATA_GROUP_
            DATA    001BH     0002H     UNIT         ?DT?_LCD9648_WRITE16ENCHAR1?LCD9648
                    001DH     0003H                  *** GAP ***
            BIT     0020H.0   0000H.1   UNIT         ?BI?MAIN
                    0020H.1   0000H.7                *** GAP ***
            DATA    0021H     002FH     UNIT         ?DT?MAIN
            DATA    0050H     0006H     UNIT         ?DT?DS18B20_READ_TEMPERTURE?DS18B20
            IDATA   0056H     0001H     UNIT         ?STACK

            * * * * * * *   C O D E   M E M O R Y   * * * * * * *
            CODE    0000H     0003H     ABSOLUTE     
            CODE    0003H     0008H     UNIT         ?PR?DS18B20_START?DS18B20
            CODE    000BH     0003H     ABSOLUTE     
            CODE    000EH     0891H     UNIT         ?CO?LCD9648
            CODE    089FH     03EFH     UNIT         ?C?LIB_CODE
            CODE    0C8EH     022CH     UNIT         ?PR?KEY_PROC?MAIN
            CODE    0EBAH     016CH     UNIT         ?PR?LCD9648_INIT_PROC?MAIN
            CODE    1026H     015EH     UNIT         ?PR?LCD_PROC?MAIN
            CODE    1184H     00C3H     UNIT         ?PR?_LCD9648_WRITE16CNCHAR?LCD9648
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  00:30:49  PAGE 2


            CODE    1247H     00ACH     UNIT         ?PR?_LCD9648_WRITE16ENCHAR?LCD9648
            CODE    12F3H     0091H     UNIT         ?PR?_FLOAT_TO_STRING?MAIN
            CODE    1384H     008EH     UNIT         ?PR?_LCD9648_WRITE16ENCHAR1?LCD9648
            CODE    1412H     008CH     UNIT         ?C_C51STARTUP
            CODE    149EH     0071H     UNIT         ?CO?MAIN
            CODE    150FH     0071H     UNIT         ?PR?DS18B20_READ_TEMPERTURE?DS18B20
            CODE    1580H     0068H     UNIT         ?PR?LCD9648_INIT?LCD9648
            CODE    15E8H     003BH     UNIT         ?PR?DS18B20_CHECK?DS18B20
            CODE    1623H     0034H     UNIT         ?PR?_DS18B20_WRITE_BYTE?DS18B20
            CODE    1657H     0033H     UNIT         ?PR?TIMER0_ISR?MAIN
            CODE    168AH     002EH     UNIT         ?C_INITSEG
            CODE    16B8H     002DH     UNIT         ?PR?LCD9648_CLEAR?LCD9648
            CODE    16E5H     0020H     UNIT         ?PR?LCD_INIT_TEST?MAIN
            CODE    1705H     001BH     UNIT         ?PR?DS18B20_READ_BYTE?DS18B20
            CODE    1720H     001BH     UNIT         ?PR?_WRITEDATA?LCD9648
            CODE    173BH     001AH     UNIT         ?PR?DS18B20_READ_BIT?DS18B20
            CODE    1755H     0019H     UNIT         ?PR?_SENDDATASPI?LCD9648
            CODE    176EH     0017H     UNIT         ?PR?MAIN?MAIN
            CODE    1785H     0016H     UNIT         ?PR?KEY_READ?KEY
            CODE    179BH     0013H     UNIT         ?PR?TIMER0_INIT?MAIN
            CODE    17AEH     0012H     UNIT         ?PR?_WRITECOMM?LCD9648
            CODE    17C0H     000FH     UNIT         ?PR?_DELAY_10US?DS18B20
            CODE    17CFH     000EH     UNIT         ?PR?DS18B20_RESET?DS18B20
            CODE    17DDH     0007H     UNIT         ?PR?DS18B20_INIT?DS18B20



OVERLAY MAP OF MODULE:   .\Objects\Project (MAIN)


SEGMENT                                       DATA_GROUP 
  +--> CALLED SEGMENT                       START    LENGTH
-----------------------------------------------------------
?C_C51STARTUP                               -----    -----
  +--> ?PR?MAIN?MAIN
  +--> ?C_INITSEG

?PR?MAIN?MAIN                               -----    -----
  +--> ?PR?LCD9648_INIT_PROC?MAIN
  +--> ?PR?TIMER0_INIT?MAIN
  +--> ?PR?LCD_INIT_TEST?MAIN
  +--> ?PR?KEY_PROC?MAIN
  +--> ?PR?LCD_PROC?MAIN

?PR?LCD9648_INIT_PROC?MAIN                  -----    -----
  +--> ?PR?LCD9648_INIT?LCD9648
  +--> ?PR?LCD9648_CLEAR?LCD9648
  +--> ?CO?MAIN
  +--> ?PR?_LCD9648_WRITE16CNCHAR?LCD9648
  +--> ?PR?_LCD9648_WRITE16ENCHAR?LCD9648

?PR?LCD9648_INIT?LCD9648                    -----    -----
  +--> ?PR?_WRITECOMM?LCD9648

?PR?_WRITECOMM?LCD9648                      -----    -----
  +--> ?PR?_SENDDATASPI?LCD9648

BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  00:30:49  PAGE 3


?PR?LCD9648_CLEAR?LCD9648                   -----    -----
  +--> ?PR?_WRITECOMM?LCD9648
  +--> ?PR?_WRITEDATA?LCD9648

?PR?_WRITEDATA?LCD9648                      -----    -----
  +--> ?PR?_SENDDATASPI?LCD9648

?PR?_LCD9648_WRITE16CNCHAR?LCD9648          0008H    0004H
  +--> ?PR?_WRITECOMM?LCD9648
  +--> ?CO?LCD9648
  +--> ?PR?_WRITEDATA?LCD9648

?PR?_LCD9648_WRITE16ENCHAR?LCD9648          0012H    0004H
  +--> ?PR?_WRITECOMM?LCD9648
  +--> ?CO?LCD9648
  +--> ?PR?_WRITEDATA?LCD9648

?PR?LCD_INIT_TEST?MAIN                      -----    -----
  +--> ?PR?LCD9648_INIT_PROC?MAIN

?PR?KEY_PROC?MAIN                           -----    -----
  +--> ?PR?KEY_READ?KEY

?PR?LCD_PROC?MAIN                           0008H    000AH
  +--> ?PR?LCD9648_CLEAR?LCD9648
  +--> ?CO?MAIN
  +--> ?PR?_LCD9648_WRITE16ENCHAR?LCD9648
  +--> ?PR?_FLOAT_TO_STRING?MAIN

?PR?_FLOAT_TO_STRING?MAIN                   0012H    0009H



SYMBOL TABLE OF MODULE:  .\Objects\Project (MAIN)

  VALUE           TYPE          NAME
  ----------------------------------

  -------         MODULE        MAIN
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0080H         PUBLIC        P0
  D:0090H         PUBLIC        P1
  D:00A0H         PUBLIC        P2
  D:00B0H         PUBLIC        P3
  C:1657H         PUBLIC        Timer0_ISR
  B:00A8H.7       PUBLIC        EA
  D:00A8H         PUBLIC        IE
  D:0021H         PUBLIC        Temperature
  D:0022H         PUBLIC        Display_Buffer
  D:00B8H         PUBLIC        IP
  C:0EBAH         PUBLIC        LCD9648_Init_Proc
  B:0020H.0       PUBLIC        LCD_Init_End_Flag
  C:0C8EH         PUBLIC        Key_Proc
  D:0032H         PUBLIC        Key_Down
  C:176EH         PUBLIC        main
  C:179BH         PUBLIC        Timer0_Init
  D:0033H         PUBLIC        Key_Old
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  00:30:49  PAGE 4


  D:0034H         PUBLIC        Key_Slow_Down
  D:0035H         PUBLIC        LCD_Disp_Mode
  D:0036H         PUBLIC        Key_Val
  D:0037H         PUBLIC        Last_LCD_Mode
  D:0038H         PUBLIC        LCD_Init_Mode
  D:0098H         PUBLIC        SCON
  D:0089H         PUBLIC        TMOD
  D:0088H         PUBLIC        TCON
  D:0039H         PUBLIC        Timer0_count
  B:00A8H.1       PUBLIC        ET0
  C:1026H         PUBLIC        LCD_Proc
  D:008CH         PUBLIC        TH0
  C:16E5H         PUBLIC        LCD_Init_Test
  D:003BH         PUBLIC        Key_Up
  D:008AH         PUBLIC        TL0
  D:003CH         PUBLIC        Last_State_Index
  B:0088H.4       PUBLIC        TR0
  D:003DH         PUBLIC        Time_Flag_Count
  D:003EH         PUBLIC        State_Index
  D:00C8H         PUBLIC        T2CON
  D:003FH         PUBLIC        Time_Flag
  D:0040H         PUBLIC        State_Disp
  C:1304H         PUBLIC        _Float_To_String
  D:00D0H         PUBLIC        PSW
  -------         PROC          KEY_PROC
  C:0C8EH         LINE#         28
  C:0C8EH         LINE#         29
  C:0C8EH         LINE#         30
  C:0C95H         LINE#         31
  C:0C98H         LINE#         33
  C:0C9DH         LINE#         34
  C:0CA6H         LINE#         35
  C:0CACH         LINE#         36
  C:0CAFH         LINE#         38
  C:0CCBH         LINE#         39
  C:0CCBH         LINE#         40
  C:0CCBH         LINE#         41
  C:0CD4H         LINE#         42
  C:0CD4H         LINE#         43
  C:0CDFH         LINE#         44
  C:0CDFH         LINE#         45
  C:0CFCH         LINE#         46
  C:0D28H         LINE#         47
  C:0D5CH         LINE#         48
  C:0D5DH         LINE#         49
  C:0D6CH         LINE#         50
  C:0D6CH         LINE#         51
  C:0D8AH         LINE#         52
  C:0DB5H         LINE#         53
  C:0DB5H         LINE#         54
  C:0DB5H         LINE#         55
  C:0DB6H         LINE#         57
  C:0DB6H         LINE#         58
  C:0DBFH         LINE#         59
  C:0DBFH         LINE#         60
  C:0DCAH         LINE#         61
  C:0DCAH         LINE#         62
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  00:30:49  PAGE 5


  C:0DE7H         LINE#         63
  C:0E15H         LINE#         64
  C:0E4BH         LINE#         65
  C:0E4CH         LINE#         66
  C:0E58H         LINE#         67
  C:0E58H         LINE#         68
  C:0E76H         LINE#         69
  C:0E9EH         LINE#         70
  C:0E9EH         LINE#         71
  C:0E9EH         LINE#         72
  C:0E9FH         LINE#         74
  C:0E9FH         LINE#         75
  C:0EA9H         LINE#         76
  C:0EAAH         LINE#         78
  C:0EAAH         LINE#         79
  C:0EAFH         LINE#         80
  C:0EAFH         LINE#         81
  C:0EB9H         LINE#         82
  C:0EB9H         LINE#         83
  C:0EB9H         LINE#         84
  C:0EB9H         LINE#         85
  -------         ENDPROC       KEY_PROC
  C:12FBH         SYMBOL        L?0065
  -------         PROC          L?0064
  -------         ENDPROC       L?0064
  C:12FBH         SYMBOL        L?0065
  -------         PROC          _FLOAT_TO_STRING
  D:0012H         SYMBOL        value
  D:0016H         SYMBOL        str
  D:0019H         SYMBOL        unit
  -------         DO            
  D:001AH         SYMBOL        integer_part
  D:0007H         SYMBOL        decimal_part
  -------         ENDDO         
  C:1304H         LINE#         89
  C:1312H         LINE#         90
  C:1312H         LINE#         91
  C:1317H         LINE#         92
  C:133DH         LINE#         94
  C:134EH         LINE#         95
  C:1364H         LINE#         96
  C:136CH         LINE#         97
  C:1375H         LINE#         98
  C:137DH         LINE#         99
  -------         ENDPROC       _FLOAT_TO_STRING
  -------         PROC          TIMER0_INIT
  C:179BH         LINE#         106
  C:179BH         LINE#         107
  C:179BH         LINE#         108
  C:179EH         LINE#         109
  C:17A1H         LINE#         110
  C:17A4H         LINE#         111
  C:17A7H         LINE#         112
  C:17A9H         LINE#         113
  C:17ABH         LINE#         114
  C:17ADH         LINE#         115
  -------         ENDPROC       TIMER0_INIT
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  00:30:49  PAGE 6


  -------         PROC          TIMER0_ISR
  C:1657H         LINE#         121
  C:165BH         LINE#         123
  C:165EH         LINE#         124
  C:1661H         LINE#         126
  C:1669H         LINE#         128
  C:1672H         LINE#         129
  C:1672H         LINE#         130
  C:1675H         LINE#         131
  C:167BH         LINE#         133
  C:167BH         LINE#         135
  C:1685H         LINE#         136
  -------         ENDPROC       TIMER0_ISR
  -------         PROC          LCD9648_INIT_PROC
  C:0EBAH         LINE#         137
  C:0EBAH         LINE#         138
  C:0EBAH         LINE#         139
  C:0ED0H         LINE#         140
  C:0ED0H         LINE#         141
  C:0ED0H         LINE#         142
  C:0ED3H         LINE#         143
  C:0ED6H         LINE#         145
  C:0EE2H         LINE#         146
  C:0EEFH         LINE#         147
  C:0EFCH         LINE#         149
  C:0F09H         LINE#         151
  C:0F16H         LINE#         152
  C:0F23H         LINE#         153
  C:0F30H         LINE#         154
  C:0F3DH         LINE#         155
  C:0F4AH         LINE#         156
  C:0F4AH         LINE#         158
  C:0F4AH         LINE#         159
  C:0F4DH         LINE#         160
  C:0F50H         LINE#         162
  C:0F5CH         LINE#         163
  C:0F69H         LINE#         164
  C:0F76H         LINE#         165
  C:0F83H         LINE#         166
  C:0F90H         LINE#         167
  C:0F9DH         LINE#         169
  C:0FAAH         LINE#         170
  C:0FB7H         LINE#         171
  C:0FC4H         LINE#         172
  C:0FD1H         LINE#         173
  C:0FDEH         LINE#         174
  C:0FEBH         LINE#         176
  C:0FF8H         LINE#         177
  C:1005H         LINE#         178
  C:1012H         LINE#         179
  C:101FH         LINE#         180
  C:101FH         LINE#         182
  C:101FH         LINE#         183
  C:1022H         LINE#         184
  C:1025H         LINE#         192
  C:1025H         LINE#         194
  C:1025H         LINE#         196
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  00:30:49  PAGE 7


  C:1025H         LINE#         197
  C:1025H         LINE#         198
  -------         ENDPROC       LCD9648_INIT_PROC
  -------         PROC          LCD_INIT_TEST
  C:16E5H         LINE#         200
  C:16E5H         LINE#         201
  C:16E5H         LINE#         202
  C:16E9H         LINE#         203
  C:16E9H         LINE#         204
  C:16ECH         LINE#         205
  C:16EEH         LINE#         206
  C:16F0H         LINE#         207
  C:16F7H         LINE#         208
  C:16F7H         LINE#         209
  C:16FAH         LINE#         210
  C:16FAH         LINE#         211
  C:16FFH         LINE#         212
  C:1704H         LINE#         213
  C:1704H         LINE#         215
  -------         ENDPROC       LCD_INIT_TEST
  -------         PROC          LCD_PROC
  -------         DO            
  D:0008H         SYMBOL        temp_str
  D:0010H         SYMBOL        i
  D:0011H         SYMBOL        need_update
  -------         ENDDO         
  C:1026H         LINE#         217
  C:1026H         LINE#         218
  C:1026H         LINE#         221
  C:1029H         LINE#         223
  C:102FH         LINE#         226
  C:103AH         LINE#         227
  C:103AH         LINE#         228
  C:103DH         LINE#         229
  C:1040H         LINE#         230
  C:1043H         LINE#         231
  C:1046H         LINE#         232
  C:1046H         LINE#         234
  C:104DH         LINE#         235
  C:104DH         LINE#         236
  C:1051H         LINE#         237
  C:1051H         LINE#         239
  C:105EH         LINE#         242
  C:106AH         LINE#         243
  C:1077H         LINE#         244
  C:1084H         LINE#         245
  C:1084H         LINE#         248
  C:1087H         LINE#         249
  C:1094H         LINE#         251
  C:109FH         LINE#         252
  C:10ACH         LINE#         254
  C:10C0H         LINE#         255
  C:10CAH         LINE#         256
  C:10CDH         LINE#         257
  C:10D6H         LINE#         258
  C:10D6H         LINE#         259
  C:10DAH         LINE#         260
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  00:30:49  PAGE 8


  C:10DAH         LINE#         262
  C:10E7H         LINE#         265
  C:10EAH         LINE#         266
  C:10EAH         LINE#         267
  C:10EFH         LINE#         268
  C:10FAH         LINE#         270
  C:110AH         LINE#         271
  C:1113H         LINE#         274
  C:1120H         LINE#         275
  C:112DH         LINE#         276
  C:113AH         LINE#         277
  C:113AH         LINE#         280
  C:113DH         LINE#         281
  C:114AH         LINE#         283
  C:1155H         LINE#         284
  C:1162H         LINE#         286
  C:1176H         LINE#         287
  C:1183H         LINE#         288
  C:1183H         LINE#         289
  -------         ENDPROC       LCD_PROC
  -------         PROC          MAIN
  C:176EH         LINE#         292
  C:176EH         LINE#         293
  C:176EH         LINE#         295
  C:1771H         LINE#         296
  C:1774H         LINE#         298
  C:1777H         LINE#         299
  C:177AH         LINE#         301
  C:177AH         LINE#         302
  C:177AH         LINE#         303
  C:177DH         LINE#         304
  C:1780H         LINE#         305
  C:1783H         LINE#         306
  -------         ENDPROC       MAIN
  -------         ENDMOD        MAIN

  -------         MODULE        DS18B20
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  C:17CFH         PUBLIC        ds18b20_reset
  D:00A8H         PUBLIC        IE
  C:0003H         PUBLIC        ds18b20_start
  D:00B8H         PUBLIC        IP
  C:1705H         PUBLIC        ds18b20_read_byte
  C:17DDH         PUBLIC        ds18b20_init
  C:173BH         PUBLIC        ds18b20_read_bit
  C:162BH         PUBLIC        _ds18b20_write_byte
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  C:150FH         PUBLIC        ds18b20_read_temperture
  C:17C0H         PUBLIC        _delay_10us
  B:00B0H.7       PUBLIC        DS18B20_PORT
  D:00C8H         PUBLIC        T2CON
  C:15E8H         PUBLIC        ds18b20_check
  D:00D0H         PUBLIC        PSW
  -------         PROC          _DELAY_10US
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  00:30:49  PAGE 9


  D:0007H         SYMBOL        xms
  -------         DO            
  D:0006H         SYMBOL        i
  -------         ENDDO         
  C:17C0H         LINE#         4
  C:17C0H         LINE#         5
  C:17C0H         LINE#         7
  C:17C6H         LINE#         8
  C:17C6H         LINE#         9
  C:17C8H         LINE#         10
  C:17CEH         LINE#         12
  -------         ENDPROC       _DELAY_10US
  -------         PROC          DS18B20_RESET
  C:17CFH         LINE#         20
  C:17CFH         LINE#         21
  C:17CFH         LINE#         22
  C:17D1H         LINE#         23
  C:17D6H         LINE#         24
  C:17D8H         LINE#         25
  -------         ENDPROC       DS18B20_RESET
  -------         PROC          DS18B20_CHECK
  -------         DO            
  D:0004H         SYMBOL        time_temp
  -------         ENDDO         
  C:15E8H         LINE#         34
  C:15E8H         LINE#         35
  C:15E8H         LINE#         36
  C:15EAH         LINE#         38
  C:15F3H         LINE#         39
  C:15F3H         LINE#         40
  C:15F4H         LINE#         41
  C:15F9H         LINE#         42
  C:15FBH         LINE#         43
  C:1604H         LINE#         44
  C:1606H         LINE#         45
  C:160FH         LINE#         46
  C:160FH         LINE#         47
  C:1610H         LINE#         48
  C:1615H         LINE#         49
  C:1617H         LINE#         50
  C:1620H         LINE#         51
  C:1622H         LINE#         52
  -------         ENDPROC       DS18B20_CHECK
  -------         PROC          DS18B20_READ_BIT
  -------         DO            
  D:0004H         SYMBOL        dat
  -------         ENDDO         
  C:173BH         LINE#         60
  C:173BH         LINE#         61
  C:173BH         LINE#         62
  C:173DH         LINE#         64
  C:173FH         LINE#         65
  C:1741H         LINE#         66
  C:1743H         LINE#         67
  C:1745H         LINE#         68
  C:174BH         LINE#         69
  C:174DH         LINE#         70
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  00:30:49  PAGE 10


  C:1752H         LINE#         71
  C:1754H         LINE#         72
  -------         ENDPROC       DS18B20_READ_BIT
  -------         PROC          DS18B20_READ_BYTE
  -------         DO            
  D:0003H         SYMBOL        i
  D:0002H         SYMBOL        dat
  D:0007H         SYMBOL        temp
  -------         ENDDO         
  C:1705H         LINE#         80
  C:1705H         LINE#         81
  C:1705H         LINE#         82
  C:1707H         LINE#         83
  C:1708H         LINE#         84
  C:1709H         LINE#         86
  C:1709H         LINE#         87
  C:1709H         LINE#         88
  C:170CH         LINE#         89
  C:1719H         LINE#         90
  C:171DH         LINE#         91
  C:171FH         LINE#         92
  -------         ENDPROC       DS18B20_READ_BYTE
  -------         PROC          L?0035
  -------         ENDPROC       L?0035
  -------         PROC          _DS18B20_WRITE_BYTE
  D:0004H         SYMBOL        dat
  -------         DO            
  D:0003H         SYMBOL        i
  D:0002H         SYMBOL        temp
  -------         ENDDO         
  C:162BH         LINE#         100
  C:162DH         LINE#         101
  C:162DH         LINE#         102
  C:162FH         LINE#         103
  C:162FH         LINE#         105
  C:162FH         LINE#         106
  C:162FH         LINE#         107
  C:1633H         LINE#         108
  C:1637H         LINE#         109
  C:163AH         LINE#         110
  C:163AH         LINE#         111
  C:163CH         LINE#         112
  C:163EH         LINE#         113
  C:1640H         LINE#         114
  C:1645H         LINE#         115
  C:1647H         LINE#         117
  C:1647H         LINE#         118
  C:1649H         LINE#         119
  C:164EH         LINE#         120
  C:1650H         LINE#         121
  C:1652H         LINE#         122
  C:1652H         LINE#         123
  C:1656H         LINE#         124
  -------         ENDPROC       _DS18B20_WRITE_BYTE
  -------         PROC          DS18B20_START
  C:0003H         LINE#         132
  C:0003H         LINE#         133
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  00:30:49  PAGE 11


  C:0003H         LINE#         134
  C:0003H         LINE#         135
  C:0003H         LINE#         136
  C:0006H         LINE#         137
  -------         ENDPROC       DS18B20_START
  -------         PROC          DS18B20_INIT
  C:17DDH         LINE#         146
  C:17DDH         LINE#         147
  C:17DDH         LINE#         148
  C:17E0H         LINE#         149
  C:17E3H         LINE#         150
  -------         ENDPROC       DS18B20_INIT
  -------         PROC          DS18B20_READ_TEMPERTURE
  -------         DO            
  D:0050H         SYMBOL        temp
  D:0007H         SYMBOL        dath
  D:0001H         SYMBOL        datl
  D:0054H         SYMBOL        value
  -------         ENDDO         
  C:150FH         LINE#         158
  C:150FH         LINE#         159
  C:150FH         LINE#         161
  C:1511H         LINE#         162
  C:1512H         LINE#         163
  C:1516H         LINE#         165
  C:1519H         LINE#         166
  C:1519H         LINE#         167
  C:1519H         LINE#         168
  C:151CH         LINE#         169
  C:1521H         LINE#         171
  C:1526H         LINE#         172
  C:1529H         LINE#         173
  C:1535H         LINE#         175
  C:153CH         LINE#         176
  C:153CH         LINE#         177
  C:154DH         LINE#         178
  C:155BH         LINE#         179
  C:155DH         LINE#         181
  C:155DH         LINE#         182
  C:1577H         LINE#         183
  C:1577H         LINE#         184
  C:157FH         LINE#         185
  -------         ENDPROC       DS18B20_READ_TEMPERTURE
  -------         ENDMOD        DS18B20

  -------         MODULE        LCD9648
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0080H         PUBLIC        P0
  D:0090H         PUBLIC        P1
  D:00A0H         PUBLIC        P2
  D:00B0H         PUBLIC        P3
  C:1731H         PUBLIC        _WriteData
  C:1384H         PUBLIC        _LCD9648_Write16EnCHAR1
  D:00A8H         PUBLIC        IE
  D:00B8H         PUBLIC        IP
  C:17B6H         PUBLIC        _WriteComm
  B:00A0H.7       PUBLIC        RS
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  00:30:49  PAGE 12


  C:1580H         PUBLIC        LCD9648_Init
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  B:0080H.0       PUBLIC        CS0
  C:000EH         PUBLIC        CN16CHARfont
  C:0250H         PUBLIC        EN16CHARfont
  B:00A0H.5       PUBLIC        SDA
  C:1184H         PUBLIC        _LCD9648_Write16CnCHAR
  C:1247H         PUBLIC        _LCD9648_Write16EnCHAR
  B:00A0H.6       PUBLIC        SCL
  D:00C8H         PUBLIC        T2CON
  C:1755H         PUBLIC        _SendDataSPI
  B:0080H.1       PUBLIC        RST
  C:16B8H         PUBLIC        LCD9648_Clear
  D:00D0H         PUBLIC        PSW
  -------         PROC          _SENDDATASPI
  D:0007H         SYMBOL        dat
  -------         DO            
  D:0006H         SYMBOL        i
  -------         ENDDO         
  C:1755H         LINE#         4
  C:1755H         LINE#         5
  C:1755H         LINE#         8
  C:1757H         LINE#         9
  C:1757H         LINE#         10
  C:175FH         LINE#         11
  C:1761H         LINE#         13
  C:1765H         LINE#         15
  C:1767H         LINE#         16
  C:1769H         LINE#         17
  C:176DH         LINE#         18
  -------         ENDPROC       _SENDDATASPI
  C:17B0H         SYMBOL        L?0067
  C:17B2H         SYMBOL        L?0068
  -------         PROC          L?0066
  -------         ENDPROC       L?0066
  C:17B0H         SYMBOL        L?0067
  C:17B2H         SYMBOL        L?0068
  -------         PROC          _WRITECOMM
  D:0006H         SYMBOL        i
  C:17B6H         LINE#         20
  C:17B6H         LINE#         21
  C:17B6H         LINE#         23
  C:17B8H         LINE#         24
  C:17BAH         LINE#         26
  C:17BDH         LINE#         28
  C:17BFH         LINE#         30
  -------         ENDPROC       _WRITECOMM
  C:1725H         SYMBOL        L?0070
  -------         PROC          L?0069
  -------         ENDPROC       L?0069
  C:1725H         SYMBOL        L?0070
  -------         PROC          _WRITEDATA
  D:0006H         SYMBOL        i
  C:1731H         LINE#         31
  C:1731H         LINE#         32
  C:1731H         LINE#         33
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  00:30:49  PAGE 13


  C:1733H         LINE#         34
  C:1735H         LINE#         36
  C:1738H         LINE#         38
  C:173AH         LINE#         39
  -------         ENDPROC       _WRITEDATA
  -------         PROC          LCD9648_INIT
  -------         DO            
  D:0006H         SYMBOL        i
  -------         ENDDO         
  C:1580H         LINE#         41
  C:1580H         LINE#         42
  C:1580H         LINE#         46
  C:1582H         LINE#         47
  C:1590H         LINE#         49
  C:1592H         LINE#         50
  C:15A0H         LINE#         52
  C:15A2H         LINE#         53
  C:15B0H         LINE#         55
  C:15B7H         LINE#         56
  C:15BEH         LINE#         57
  C:15C5H         LINE#         58
  C:15CCH         LINE#         59
  C:15D3H         LINE#         60
  C:15DAH         LINE#         61
  C:15E1H         LINE#         62
  -------         ENDPROC       LCD9648_INIT
  -------         PROC          LCD9648_CLEAR
  -------         DO            
  D:0005H         SYMBOL        i
  D:0004H         SYMBOL        j
  -------         ENDDO         
  C:16B8H         LINE#         67
  C:16B8H         LINE#         68
  C:16B8H         LINE#         71
  C:16BAH         LINE#         72
  C:16BAH         LINE#         73
  C:16C1H         LINE#         74
  C:16C7H         LINE#         75
  C:16CEH         LINE#         76
  C:16D4H         LINE#         78
  C:16D6H         LINE#         79
  C:16D6H         LINE#         80
  C:16DCH         LINE#         81
  C:16E0H         LINE#         82
  C:16E4H         LINE#         83
  -------         ENDPROC       LCD9648_CLEAR
  -------         PROC          _LCD9648_WRITE16CNCHAR
  D:0008H         SYMBOL        x
  D:0009H         SYMBOL        y
  D:0001H         SYMBOL        cn
  -------         DO            
  D:0004H         SYMBOL        j
  D:000AH         SYMBOL        x1
  D:000BH         SYMBOL        x2
  D:0005H         SYMBOL        wordNum
  -------         ENDDO         
  C:1184H         LINE#         86
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  00:30:49  PAGE 14


  C:1188H         LINE#         87
  C:1188H         LINE#         91
  C:118FH         LINE#         92
  C:118FH         LINE#         93
  C:1192H         LINE#         94
  C:1192H         LINE#         97
  C:1199H         LINE#         98
  C:1199H         LINE#         99
  C:119CH         LINE#         100
  C:119CH         LINE#         101
  C:11A2H         LINE#         103
  C:11A8H         LINE#         104
  C:11B0H         LINE#         105
  C:11B0H         LINE#         108
  C:11B7H         LINE#         110
  C:11BEH         LINE#         111
  C:11C4H         LINE#         113
  C:11C7H         LINE#         114
  C:11CEH         LINE#         115
  C:11D0H         LINE#         116
  C:11D0H         LINE#         118
  C:1203H         LINE#         120
  C:1203H         LINE#         121
  C:1204H         LINE#         122
  C:1204H         LINE#         123
  C:1209H         LINE#         124
  C:1209H         LINE#         126
  C:1210H         LINE#         129
  C:1213H         LINE#         130
  C:121AH         LINE#         131
  C:121AH         LINE#         132
  C:122AH         LINE#         133
  C:122EH         LINE#         134
  C:1234H         LINE#         135
  C:1234H         LINE#         136
  C:123AH         LINE#         137
  C:1241H         LINE#         139
  C:1244H         LINE#         140
  C:1246H         LINE#         141
  -------         ENDPROC       _LCD9648_WRITE16CNCHAR
  -------         PROC          _LCD9648_WRITE16ENCHAR
  D:0012H         SYMBOL        x
  D:0013H         SYMBOL        y
  D:0001H         SYMBOL        en
  -------         DO            
  D:0004H         SYMBOL        j
  D:0014H         SYMBOL        x1
  D:0015H         SYMBOL        x2
  D:0005H         SYMBOL        wordNum
  -------         ENDDO         
  C:1247H         LINE#         143
  C:124BH         LINE#         144
  C:124BH         LINE#         148
  C:1252H         LINE#         149
  C:1252H         LINE#         150
  C:1255H         LINE#         151
  C:1255H         LINE#         154
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  00:30:49  PAGE 15


  C:125CH         LINE#         155
  C:125CH         LINE#         156
  C:125FH         LINE#         157
  C:125FH         LINE#         158
  C:1265H         LINE#         160
  C:126BH         LINE#         161
  C:1273H         LINE#         162
  C:1273H         LINE#         165
  C:127AH         LINE#         167
  C:1281H         LINE#         168
  C:1287H         LINE#         170
  C:128CH         LINE#         171
  C:1293H         LINE#         172
  C:1295H         LINE#         173
  C:1295H         LINE#         175
  C:12ADH         LINE#         176
  C:12ADH         LINE#         177
  C:12AEH         LINE#         178
  C:12AEH         LINE#         179
  C:12B3H         LINE#         180
  C:12B3H         LINE#         182
  C:12BAH         LINE#         185
  C:12BFH         LINE#         186
  C:12C6H         LINE#         187
  C:12C6H         LINE#         188
  C:12D6H         LINE#         189
  C:12DAH         LINE#         190
  C:12E0H         LINE#         191
  C:12E0H         LINE#         192
  C:12E6H         LINE#         193
  C:12EDH         LINE#         195
  C:12F0H         LINE#         196
  C:12F2H         LINE#         197
  -------         ENDPROC       _LCD9648_WRITE16ENCHAR
  -------         PROC          _LCD9648_WRITE16ENCHAR1
  D:0001H         SYMBOL        x
  D:0005H         SYMBOL        y
  D:0003H         SYMBOL        en
  -------         DO            
  D:0002H         SYMBOL        j
  D:001BH         SYMBOL        x1
  D:001CH         SYMBOL        x2
  D:0004H         SYMBOL        wordNum
  -------         ENDDO         
  C:1384H         LINE#         198
  C:1386H         LINE#         199
  C:1386H         LINE#         203
  C:138CH         LINE#         204
  C:138CH         LINE#         205
  C:138FH         LINE#         206
  C:138FH         LINE#         209
  C:1395H         LINE#         210
  C:1395H         LINE#         211
  C:1398H         LINE#         212
  C:1398H         LINE#         213
  C:139CH         LINE#         215
  C:13A2H         LINE#         217
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  00:30:49  PAGE 16


  C:13A2H         LINE#         220
  C:13A9H         LINE#         222
  C:13AFH         LINE#         223
  C:13B4H         LINE#         225
  C:13B9H         LINE#         226
  C:13C0H         LINE#         227
  C:13C2H         LINE#         228
  C:13C2H         LINE#         230
  C:13D6H         LINE#         231
  C:13D6H         LINE#         232
  C:13D7H         LINE#         233
  C:13D7H         LINE#         234
  C:13DCH         LINE#         235
  C:13DCH         LINE#         237
  C:13E2H         LINE#         240
  C:13E7H         LINE#         241
  C:13EEH         LINE#         242
  C:13EEH         LINE#         243
  C:1403H         LINE#         244
  C:1407H         LINE#         245
  C:140BH         LINE#         246
  C:140BH         LINE#         247
  C:140FH         LINE#         248
  C:140FH         LINE#         250
  C:140FH         LINE#         251
  C:1411H         LINE#         252
  -------         ENDPROC       _LCD9648_WRITE16ENCHAR1
  -------         ENDMOD        LCD9648

  -------         MODULE        KEY
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  C:1785H         PUBLIC        Key_Read
  D:00A8H         PUBLIC        IE
  D:00B8H         PUBLIC        IP
  B:0090H.0       PUBLIC        k1
  B:0090H.1       PUBLIC        k2
  B:0090H.2       PUBLIC        k3
  B:0090H.3       PUBLIC        k4
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  D:00C8H         PUBLIC        T2CON
  D:00D0H         PUBLIC        PSW
  -------         PROC          KEY_READ
  -------         DO            
  D:0007H         SYMBOL        temp
  -------         ENDDO         
  C:1785H         LINE#         4
  C:1785H         LINE#         5
  C:1785H         LINE#         6
  C:1787H         LINE#         8
  C:178BH         LINE#         9
  C:1790H         LINE#         10
  C:1795H         LINE#         11
  C:179AH         LINE#         12
  C:179AH         LINE#         13
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  00:30:49  PAGE 17


  -------         ENDPROC       KEY_READ
  -------         ENDMOD        KEY

  -------         MODULE        ?C?FPADD
  C:08A6H         PUBLIC        ?C?FPADD
  C:08A2H         PUBLIC        ?C?FPSUB
  -------         ENDMOD        ?C?FPADD

  -------         MODULE        ?C?FPMUL
  C:0997H         PUBLIC        ?C?FPMUL
  -------         ENDMOD        ?C?FPMUL

  -------         MODULE        ?C?FPCMP
  C:0A9FH         PUBLIC        ?C?FPCMP
  C:0A9DH         PUBLIC        ?C?FPCMP3
  -------         ENDMOD        ?C?FPCMP

  -------         MODULE        ?C?FCAST
  C:0B20H         PUBLIC        ?C?FCASTC
  C:0B1BH         PUBLIC        ?C?FCASTI
  C:0B16H         PUBLIC        ?C?FCASTL
  -------         ENDMOD        ?C?FCAST

  -------         MODULE        ?C?CASTF
  C:0B54H         PUBLIC        ?C?CASTF
  -------         ENDMOD        ?C?CASTF

  -------         MODULE        ?C?CLDPTR
  C:0BD5H         PUBLIC        ?C?CLDPTR
  -------         ENDMOD        ?C?CLDPTR

  -------         MODULE        ?C?CLDOPTR
  C:0BEEH         PUBLIC        ?C?CLDOPTR
  -------         ENDMOD        ?C?CLDOPTR

  -------         MODULE        ?C?CSTPTR
  C:0C1BH         PUBLIC        ?C?CSTPTR
  -------         ENDMOD        ?C?CSTPTR

  -------         MODULE        ?C?CSTOPTR
  C:0C2DH         PUBLIC        ?C?CSTOPTR
  -------         ENDMOD        ?C?CSTOPTR

  -------         MODULE        ?C?LNEG
  C:0C4FH         PUBLIC        ?C?LNEG
  -------         ENDMOD        ?C?LNEG

  -------         MODULE        ?C?LLDIDATA
  C:0C5DH         PUBLIC        ?C?LLDIDATA
  -------         ENDMOD        ?C?LLDIDATA

  -------         MODULE        ?C?LSTIDATA
  C:0C69H         PUBLIC        ?C?LSTIDATA
  -------         ENDMOD        ?C?LSTIDATA

  -------         MODULE        ?C?LSTKIDATA
  C:0C75H         PUBLIC        ?C?LSTKIDATA
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  00:30:49  PAGE 18


  -------         ENDMOD        ?C?LSTKIDATA

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?DS18B20_INIT?DS18B20

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?DS18B20_READ_TEMPERTURE?DS18B20

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?_LCD9648_WRITE16ENCHAR1?LCD9648

Program Size: data=83.1 xdata=0 code=6116
LINK/LOCATE RUN COMPLETE.  3 WARNING(S),  0 ERROR(S)
