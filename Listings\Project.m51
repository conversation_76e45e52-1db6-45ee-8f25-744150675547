BL<PERSON> BANKED LINKER/LOCATER V6.22                                                        07/08/2025  17:20:53  PAGE 1


BL51 BANKED LINKER/LOCATER V6.22, INVOKED BY:
E:\KEIL\C51\BIN\BL51.EXE .\Objects\main.obj, .\Objects\ds18b20.obj, .\Objects\lcd9648.obj, .\Objects\Key.obj, .\Objects\
>> rtc3085.obj TO .\Objects\Project PRINT (.\Listings\Project.m51) RAMSIZE (256)


MEMORY MODEL: SMALL WITH FLOATING POINT ARITHMETIC


INPUT MODULES INCLUDED:
  .\Objects\main.obj (MAIN)
  .\Objects\ds18b20.obj (DS18B20)
  .\Objects\lcd9648.obj (LCD9648)
  .\Objects\Key.obj (KEY)
  .\Objects\rtc3085.obj (RTC3085)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?FPADD)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?FPMUL)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?FPCMP)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?FCAST)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?CASTF)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?FPGETOPN)
  E:\KEIL\C51\LIB\C51S.LIB (?C_STARTUP)
  E:\KEIL\C51\LIB\C51S.LIB (?C?CLDPTR)
  E:\KEIL\C51\LIB\C51S.LIB (?C?CLDOPTR)
  E:\KEIL\C51\LIB\C51S.LIB (?C?CSTPTR)
  E:\KEIL\C51\LIB\C51S.LIB (?C?CSTOPTR)
  E:\KEIL\C51\LIB\C51S.LIB (?C?LNEG)
  E:\KEIL\C51\LIB\C51S.LIB (?C?LLDIDATA)
  E:\KEIL\C51\LIB\C51S.LIB (?C?LSTIDATA)
  E:\KEIL\C51\LIB\C51S.LIB (?C?LSTKIDATA)
  E:\KEIL\C51\LIB\C51S.LIB (?C_INIT)


LINK MAP OF MODULE:  .\Objects\Project (MAIN)


            TYPE    BASE      LENGTH    RELOCATION   SEGMENT NAME
            -----------------------------------------------------

            * * * * * * *   D A T A   M E M O R Y   * * * * * * *
            REG     0000H     0008H     ABSOLUTE     "REG BANK 0"
            DATA    0008H     0006H     UNIT         ?DT?RTC3085
            DATA    000EH     0002H     UNIT         ?DT?_LCD9648_WRITE16ENCHAR1?LCD9648
                    0010H     0010H                  *** GAP ***
            BIT     0020H.0   0000H.1   UNIT         ?BI?MAIN
                    0020H.1   0000H.7                *** GAP ***
            DATA    0021H     0045H     UNIT         ?DT?MAIN
            IDATA   0066H     0001H     UNIT         ?STACK

            * * * * * * *   C O D E   M E M O R Y   * * * * * * *
            CODE    0000H     0003H     ABSOLUTE     
            CODE    0003H     0008H     UNIT         ?PR?DS18B20_START?DS18B20
            CODE    000BH     0003H     ABSOLUTE     
            CODE    000EH     0891H     UNIT         ?CO?LCD9648
            CODE    089FH     03EFH     UNIT         ?C?LIB_CODE
            CODE    0C8EH     02C4H     UNIT         ?PR?KEY_PROC?MAIN
            CODE    0F52H     0277H     UNIT         ?PR?LCD_PROC?MAIN
            CODE    11C9H     016CH     UNIT         ?PR?LCD9648_INIT_PROC?MAIN
            CODE    1335H     00C3H     UNIT         ?PR?_LCD9648_WRITE16CNCHAR?LCD9648
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  17:20:53  PAGE 2


            CODE    13F8H     00ACH     UNIT         ?PR?_LCD9648_WRITE16ENCHAR?LCD9648
            CODE    14A4H     0091H     UNIT         ?PR?_FLOAT_TO_STRING?MAIN
            CODE    1535H     0091H     UNIT         ?CO?MAIN
            CODE    15C6H     008EH     UNIT         ?PR?_LCD9648_WRITE16ENCHAR1?LCD9648
            CODE    1654H     008CH     UNIT         ?C_C51STARTUP
            CODE    16E0H     0074H     UNIT         ?PR?_TIME_TO_STRING?MAIN
            CODE    1754H     0071H     UNIT         ?PR?DS18B20_READ_TEMPERTURE?DS18B20
            CODE    17C5H     0068H     UNIT         ?PR?LCD9648_INIT?LCD9648
            CODE    182DH     005FH     UNIT         ?C_INITSEG
            CODE    188CH     004AH     UNIT         ?PR?_DS1302READ?RTC3085
            CODE    18D6H     003BH     UNIT         ?PR?DS18B20_CHECK?DS18B20
            CODE    1911H     0035H     UNIT         ?PR?_DS1302WRITE?RTC3085
            CODE    1946H     0034H     UNIT         ?PR?TEMPERATURE_PROC?MAIN
            CODE    197AH     0034H     UNIT         ?PR?_DS18B20_WRITE_BYTE?DS18B20
            CODE    19AEH     0033H     UNIT         ?PR?TIMER0_ISR?MAIN
            CODE    19E1H     002DH     UNIT         ?PR?LCD9648_CLEAR?LCD9648
            CODE    1A0EH     0028H     UNIT         ?PR?MAIN?MAIN
            CODE    1A36H     0023H     UNIT         ?PR?DS1302INIT?RTC3085
            CODE    1A59H     0020H     UNIT         ?PR?LCD_INIT_TEST?MAIN
            CODE    1A79H     001BH     UNIT         ?PR?DS18B20_READ_BYTE?DS18B20
            CODE    1A94H     001BH     UNIT         ?PR?_WRITEDATA?LCD9648
            CODE    1AAFH     001AH     UNIT         ?PR?DS18B20_READ_BIT?DS18B20
            CODE    1AC9H     0019H     UNIT         ?PR?_SENDDATASPI?LCD9648
            CODE    1AE2H     0016H     UNIT         ?PR?KEY_READ?KEY
            CODE    1AF8H     0016H     UNIT         ?PR?DS1302READTIME?RTC3085
            CODE    1B0EH     0013H     UNIT         ?PR?TIMER0_INIT?MAIN
            CODE    1B21H     0012H     UNIT         ?PR?_WRITECOMM?LCD9648
            CODE    1B33H     000FH     UNIT         ?PR?_BCD_TO_DEC?MAIN
            CODE    1B42H     000FH     UNIT         ?PR?_DELAY_10US?DS18B20
            CODE    1B51H     000EH     UNIT         ?PR?DS18B20_RESET?DS18B20
            CODE    1B5FH     000CH     UNIT         ?CO?RTC3085
            CODE    1B6BH     0007H     UNIT         ?PR?DS18B20_INIT?DS18B20



OVERLAY MAP OF MODULE:   .\Objects\Project (MAIN)


SEGMENT                                       DATA_GROUP 
  +--> CALLED SEGMENT                       START    LENGTH
-----------------------------------------------------------
?C_C51STARTUP                               -----    -----
  +--> ?PR?MAIN?MAIN
  +--> ?C_INITSEG

?PR?MAIN?MAIN                               -----    -----
  +--> ?PR?DS18B20_INIT?DS18B20
  +--> ?PR?DS18B20_START?DS18B20
  +--> ?PR?DS1302INIT?RTC3085
  +--> ?PR?LCD9648_INIT_PROC?MAIN
  +--> ?PR?TIMER0_INIT?MAIN
  +--> ?PR?LCD_INIT_TEST?MAIN
  +--> ?PR?KEY_PROC?MAIN
  +--> ?PR?LCD_PROC?MAIN
  +--> ?PR?TEMPERATURE_PROC?MAIN

?PR?DS18B20_INIT?DS18B20                    -----    -----
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  17:20:53  PAGE 3


  +--> ?PR?DS18B20_RESET?DS18B20
  +--> ?PR?DS18B20_CHECK?DS18B20

?PR?DS18B20_RESET?DS18B20                   -----    -----
  +--> ?PR?_DELAY_10US?DS18B20

?PR?DS18B20_CHECK?DS18B20                   -----    -----
  +--> ?PR?_DELAY_10US?DS18B20

?PR?DS18B20_START?DS18B20                   -----    -----
  +--> ?PR?_DS18B20_WRITE_BYTE?DS18B20

?PR?_DS18B20_WRITE_BYTE?DS18B20             -----    -----
  +--> ?PR?DS18B20_RESET?DS18B20
  +--> ?PR?DS18B20_CHECK?DS18B20
  +--> ?PR?_DELAY_10US?DS18B20

?PR?DS1302INIT?RTC3085                      -----    -----
  +--> ?PR?_DS1302WRITE?RTC3085
  +--> ?CO?RTC3085

?PR?LCD9648_INIT_PROC?MAIN                  -----    -----
  +--> ?PR?LCD9648_INIT?LCD9648
  +--> ?PR?LCD9648_CLEAR?LCD9648
  +--> ?CO?MAIN
  +--> ?PR?_LCD9648_WRITE16CNCHAR?LCD9648
  +--> ?PR?_LCD9648_WRITE16ENCHAR?LCD9648

?PR?LCD9648_INIT?LCD9648                    -----    -----
  +--> ?PR?_WRITECOMM?LCD9648

?PR?_WRITECOMM?LCD9648                      -----    -----
  +--> ?PR?_SENDDATASPI?LCD9648

?PR?LCD9648_CLEAR?LCD9648                   -----    -----
  +--> ?PR?_WRITECOMM?LCD9648
  +--> ?PR?_WRITEDATA?LCD9648

?PR?_WRITEDATA?LCD9648                      -----    -----
  +--> ?PR?_SENDDATASPI?LCD9648

?PR?_LCD9648_WRITE16CNCHAR?LCD9648          0000H    0004H
  +--> ?PR?_WRITECOMM?LCD9648
  +--> ?CO?LCD9648
  +--> ?PR?_WRITEDATA?LCD9648

?PR?_LCD9648_WRITE16ENCHAR?LCD9648          0013H    0004H
  +--> ?PR?_WRITECOMM?LCD9648
  +--> ?CO?LCD9648
  +--> ?PR?_WRITEDATA?LCD9648

?PR?LCD_INIT_TEST?MAIN                      -----    -----
  +--> ?PR?LCD9648_INIT_PROC?MAIN

?PR?KEY_PROC?MAIN                           -----    -----
  +--> ?PR?KEY_READ?KEY

BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  17:20:53  PAGE 4


?PR?LCD_PROC?MAIN                           0000H    0013H
  +--> ?PR?LCD9648_CLEAR?LCD9648
  +--> ?CO?MAIN
  +--> ?PR?_LCD9648_WRITE16ENCHAR?LCD9648
  +--> ?PR?_FLOAT_TO_STRING?MAIN
  +--> ?PR?_TIME_TO_STRING?MAIN

?PR?_FLOAT_TO_STRING?MAIN                   0013H    0009H

?PR?_TIME_TO_STRING?MAIN                    0013H    0003H
  +--> ?PR?DS1302READTIME?RTC3085

?PR?DS1302READTIME?RTC3085                  -----    -----
  +--> ?CO?RTC3085
  +--> ?PR?_DS1302READ?RTC3085

?PR?TEMPERATURE_PROC?MAIN                   -----    -----
  +--> ?PR?DS18B20_READ_TEMPERTURE?DS18B20

?PR?DS18B20_READ_TEMPERTURE?DS18B20         0000H    0006H
  +--> ?PR?DS18B20_START?DS18B20
  +--> ?PR?_DS18B20_WRITE_BYTE?DS18B20
  +--> ?PR?DS18B20_READ_BYTE?DS18B20

?PR?DS18B20_READ_BYTE?DS18B20               -----    -----
  +--> ?PR?DS18B20_READ_BIT?DS18B20

?PR?DS18B20_READ_BIT?DS18B20                -----    -----
  +--> ?PR?_DELAY_10US?DS18B20



IGNORED SEGMENTS:
   _DATA_GROUP_



SYMBOL TABLE OF MODULE:  .\Objects\Project (MAIN)

  VALUE           TYPE          NAME
  ----------------------------------

  -------         MODULE        MAIN
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0080H         PUBLIC        P0
  D:0090H         PUBLIC        P1
  D:00A0H         PUBLIC        P2
  D:00B0H         PUBLIC        P3
  C:19AEH         PUBLIC        Timer0_ISR
  B:0090H.5       PUBLIC        Buzzer
  B:00A8H.7       PUBLIC        EA
  C:1946H         PUBLIC        Temperature_Proc
  D:00A8H         PUBLIC        IE
  C:16E0H         PUBLIC        _Time_To_String
  D:0022H         PUBLIC        Last_LCD_Page_Mode
  D:0023H         PUBLIC        Temperature
  D:0027H         PUBLIC        Display_Buffer
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  17:20:53  PAGE 5


  D:00B8H         PUBLIC        IP
  D:0037H         PUBLIC        LCD_Page_Mode
  C:11C9H         PUBLIC        LCD9648_Init_Proc
  B:0020H.0       PUBLIC        LCD_Init_End_Flag
  C:0C8EH         PUBLIC        Key_Proc
  D:0038H         PUBLIC        Key_Down
  C:1A0EH         PUBLIC        main
  C:1B0EH         PUBLIC        Timer0_Init
  D:0039H         PUBLIC        Key_Old
  D:003AH         PUBLIC        Key_Slow_Down
  D:003BH         PUBLIC        Key_Val
  D:003CH         PUBLIC        Last_Display_Page
  D:003DH         PUBLIC        Last_LCD_Mode
  D:003EH         PUBLIC        LCD_Init_Mode
  D:0098H         PUBLIC        SCON
  D:0089H         PUBLIC        TMOD
  D:0088H         PUBLIC        TCON
  D:003FH         PUBLIC        Last_Setting_Page
  D:0040H         PUBLIC        Display_Page
  D:0041H         PUBLIC        Setting_Index
  D:0042H         PUBLIC        Timer0_count
  D:0044H         PUBLIC        Setting_Page
  B:00A8H.1       PUBLIC        ET0
  C:0F52H         PUBLIC        LCD_Proc
  D:008CH         PUBLIC        TH0
  C:1A59H         PUBLIC        LCD_Init_Test
  D:0045H         PUBLIC        Key_Up
  D:008AH         PUBLIC        TL0
  D:0046H         PUBLIC        Setting_Disp
  D:0052H         PUBLIC        Last_State_Index
  B:0088H.4       PUBLIC        TR0
  D:0053H         PUBLIC        Time_Flag_Count
  C:1B33H         PUBLIC        _BCD_To_Dec
  D:0054H         PUBLIC        State_Index
  D:00C8H         PUBLIC        T2CON
  D:0055H         PUBLIC        Time_Flag
  D:0056H         PUBLIC        State_Disp
  C:14B5H         PUBLIC        _Float_To_String
  D:00D0H         PUBLIC        PSW
  -------         PROC          _BCD_TO_DEC
  D:0007H         SYMBOL        bcd
  C:1B33H         LINE#         38
  -------         ENDPROC       _BCD_TO_DEC
  -------         PROC          _TIME_TO_STRING
  D:0013H         SYMBOL        time_str
  C:16E0H         LINE#         45
  C:16E6H         LINE#         46
  C:16E6H         LINE#         47
  C:16E9H         LINE#         50
  C:16F9H         LINE#         51
  C:170BH         LINE#         52
  C:1713H         LINE#         53
  C:1720H         LINE#         54
  C:172CH         LINE#         55
  C:1734H         LINE#         56
  C:1741H         LINE#         57
  C:174DH         LINE#         58
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  17:20:53  PAGE 6


  -------         ENDPROC       _TIME_TO_STRING
  -------         PROC          KEY_PROC
  C:0C8EH         LINE#         61
  C:0C8EH         LINE#         62
  C:0C8EH         LINE#         63
  C:0C95H         LINE#         64
  C:0C98H         LINE#         66
  C:0C9DH         LINE#         67
  C:0CA6H         LINE#         68
  C:0CACH         LINE#         69
  C:0CAFH         LINE#         71
  C:0CCBH         LINE#         72
  C:0CCBH         LINE#         73
  C:0CCBH         LINE#         74
  C:0CD4H         LINE#         75
  C:0CD4H         LINE#         76
  C:0CDFH         LINE#         77
  C:0CDFH         LINE#         78
  C:0CFCH         LINE#         79
  C:0D28H         LINE#         80
  C:0D5CH         LINE#         81
  C:0D5DH         LINE#         82
  C:0D66H         LINE#         83
  C:0D66H         LINE#         84
  C:0D84H         LINE#         85
  C:0DAFH         LINE#         86
  C:0DAFH         LINE#         87
  C:0DB0H         LINE#         88
  C:0DB9H         LINE#         89
  C:0DB9H         LINE#         90
  C:0DD7H         LINE#         91
  C:0E03H         LINE#         92
  C:0E03H         LINE#         93
  C:0E04H         LINE#         95
  C:0E04H         LINE#         96
  C:0E0DH         LINE#         97
  C:0E0DH         LINE#         98
  C:0E18H         LINE#         99
  C:0E18H         LINE#         100
  C:0E35H         LINE#         101
  C:0E63H         LINE#         102
  C:0E99H         LINE#         103
  C:0E9AH         LINE#         104
  C:0EA3H         LINE#         105
  C:0EA3H         LINE#         106
  C:0EC1H         LINE#         107
  C:0EE1H         LINE#         108
  C:0EE1H         LINE#         109
  C:0EE3H         LINE#         110
  C:0EE9H         LINE#         111
  C:0EE9H         LINE#         112
  C:0F07H         LINE#         113
  C:0F2FH         LINE#         114
  C:0F2FH         LINE#         115
  C:0F30H         LINE#         117
  C:0F30H         LINE#         118
  C:0F3AH         LINE#         119
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  17:20:53  PAGE 7


  C:0F3AH         LINE#         120
  C:0F3CH         LINE#         122
  C:0F3CH         LINE#         123
  C:0F47H         LINE#         124
  C:0F47H         LINE#         125
  C:0F51H         LINE#         126
  C:0F51H         LINE#         127
  C:0F51H         LINE#         128
  C:0F51H         LINE#         129
  -------         ENDPROC       KEY_PROC
  C:14ACH         SYMBOL        L?0095
  -------         PROC          L?0094
  -------         ENDPROC       L?0094
  C:14ACH         SYMBOL        L?0095
  -------         PROC          _FLOAT_TO_STRING
  D:0013H         SYMBOL        value
  D:0017H         SYMBOL        str
  D:001AH         SYMBOL        unit
  -------         DO            
  D:001BH         SYMBOL        integer_part
  D:0007H         SYMBOL        decimal_part
  -------         ENDDO         
  C:14B5H         LINE#         133
  C:14C3H         LINE#         134
  C:14C3H         LINE#         135
  C:14C8H         LINE#         136
  C:14EEH         LINE#         138
  C:14FFH         LINE#         139
  C:1515H         LINE#         140
  C:151DH         LINE#         141
  C:1526H         LINE#         142
  C:152EH         LINE#         143
  -------         ENDPROC       _FLOAT_TO_STRING
  -------         PROC          TIMER0_INIT
  C:1B0EH         LINE#         150
  C:1B0EH         LINE#         151
  C:1B0EH         LINE#         152
  C:1B11H         LINE#         153
  C:1B14H         LINE#         154
  C:1B17H         LINE#         155
  C:1B1AH         LINE#         156
  C:1B1CH         LINE#         157
  C:1B1EH         LINE#         158
  C:1B20H         LINE#         159
  -------         ENDPROC       TIMER0_INIT
  -------         PROC          TIMER0_ISR
  C:19AEH         LINE#         165
  C:19B2H         LINE#         167
  C:19B5H         LINE#         168
  C:19B8H         LINE#         170
  C:19C0H         LINE#         172
  C:19C9H         LINE#         173
  C:19C9H         LINE#         174
  C:19CCH         LINE#         175
  C:19D2H         LINE#         176
  C:19D2H         LINE#         178
  C:19DCH         LINE#         179
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  17:20:53  PAGE 8


  -------         ENDPROC       TIMER0_ISR
  -------         PROC          LCD9648_INIT_PROC
  C:11C9H         LINE#         181
  C:11C9H         LINE#         182
  C:11C9H         LINE#         183
  C:11DFH         LINE#         184
  C:11DFH         LINE#         185
  C:11DFH         LINE#         186
  C:11E2H         LINE#         187
  C:11E5H         LINE#         189
  C:11F1H         LINE#         190
  C:11FEH         LINE#         191
  C:120BH         LINE#         193
  C:1218H         LINE#         195
  C:1225H         LINE#         196
  C:1232H         LINE#         197
  C:123FH         LINE#         198
  C:124CH         LINE#         199
  C:1259H         LINE#         200
  C:1259H         LINE#         202
  C:1259H         LINE#         203
  C:125CH         LINE#         204
  C:125FH         LINE#         206
  C:126BH         LINE#         207
  C:1278H         LINE#         208
  C:1285H         LINE#         209
  C:1292H         LINE#         210
  C:129FH         LINE#         211
  C:12ACH         LINE#         213
  C:12B9H         LINE#         214
  C:12C6H         LINE#         215
  C:12D3H         LINE#         216
  C:12E0H         LINE#         217
  C:12EDH         LINE#         218
  C:12FAH         LINE#         220
  C:1307H         LINE#         221
  C:1314H         LINE#         222
  C:1321H         LINE#         223
  C:132EH         LINE#         224
  C:132EH         LINE#         226
  C:132EH         LINE#         227
  C:1331H         LINE#         228
  C:1334H         LINE#         229
  C:1334H         LINE#         231
  C:1334H         LINE#         233
  C:1334H         LINE#         234
  C:1334H         LINE#         235
  -------         ENDPROC       LCD9648_INIT_PROC
  -------         PROC          LCD_INIT_TEST
  C:1A59H         LINE#         237
  C:1A59H         LINE#         238
  C:1A59H         LINE#         239
  C:1A5DH         LINE#         240
  C:1A5DH         LINE#         241
  C:1A60H         LINE#         242
  C:1A62H         LINE#         243
  C:1A64H         LINE#         244
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  17:20:53  PAGE 9


  C:1A6BH         LINE#         245
  C:1A6BH         LINE#         246
  C:1A6EH         LINE#         247
  C:1A6EH         LINE#         248
  C:1A73H         LINE#         249
  C:1A78H         LINE#         250
  C:1A78H         LINE#         252
  -------         ENDPROC       LCD_INIT_TEST
  -------         PROC          TEMPERATURE_PROC
  -------         DO            
  D:0021H         SYMBOL        temp_count
  -------         ENDDO         
  C:1946H         LINE#         255
  C:1946H         LINE#         256
  C:1946H         LINE#         260
  C:194FH         LINE#         261
  C:194FH         LINE#         262
  C:1952H         LINE#         263
  C:195DH         LINE#         266
  C:1974H         LINE#         267
  C:1974H         LINE#         268
  C:1976H         LINE#         269
  C:1977H         LINE#         271
  C:1977H         LINE#         272
  C:1979H         LINE#         273
  C:1979H         LINE#         274
  C:1979H         LINE#         275
  -------         ENDPROC       TEMPERATURE_PROC
  -------         PROC          LCD_PROC
  -------         DO            
  D:0000H         SYMBOL        temp_str
  D:0010H         SYMBOL        i
  D:0011H         SYMBOL        need_clear
  D:0012H         SYMBOL        need_update_pointer
  -------         ENDDO         
  C:0F52H         LINE#         277
  C:0F52H         LINE#         278
  C:0F52H         LINE#         281
  C:0F55H         LINE#         282
  C:0F57H         LINE#         284
  C:0F5DH         LINE#         287
  C:0F63H         LINE#         288
  C:0F63H         LINE#         289
  C:0F66H         LINE#         290
  C:0F69H         LINE#         291
  C:0F6CH         LINE#         292
  C:0F6CH         LINE#         295
  C:0F72H         LINE#         296
  C:0F72H         LINE#         297
  C:0F75H         LINE#         298
  C:0F78H         LINE#         299
  C:0F78H         LINE#         301
  C:0F93H         LINE#         302
  C:0F93H         LINE#         303
  C:0F93H         LINE#         304
  C:0F97H         LINE#         305
  C:0F97H         LINE#         306
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  17:20:53  PAGE 10


  C:0FA4H         LINE#         307
  C:0FB0H         LINE#         308
  C:0FBDH         LINE#         309
  C:0FCAH         LINE#         310
  C:0FCAH         LINE#         313
  C:0FCDH         LINE#         314
  C:0FDAH         LINE#         317
  C:0FE5H         LINE#         318
  C:0FF2H         LINE#         320
  C:1006H         LINE#         321
  C:100EH         LINE#         322
  C:1010H         LINE#         324
  C:1010H         LINE#         325
  C:1014H         LINE#         326
  C:1014H         LINE#         327
  C:1021H         LINE#         328
  C:102EH         LINE#         329
  C:102EH         LINE#         332
  C:1037H         LINE#         333
  C:1041H         LINE#         334
  C:1044H         LINE#         336
  C:1044H         LINE#         337
  C:1048H         LINE#         338
  C:1048H         LINE#         339
  C:1055H         LINE#         340
  C:1062H         LINE#         341
  C:106FH         LINE#         342
  C:107CH         LINE#         343
  C:107CH         LINE#         346
  C:1084H         LINE#         347
  C:1084H         LINE#         348
  C:1087H         LINE#         349
  C:1087H         LINE#         350
  C:108CH         LINE#         351
  C:1097H         LINE#         353
  C:10A7H         LINE#         354
  C:10B0H         LINE#         355
  C:10B0H         LINE#         358
  C:10B3H         LINE#         359
  C:10C0H         LINE#         361
  C:10CBH         LINE#         362
  C:10D8H         LINE#         364
  C:10ECH         LINE#         365
  C:10F6H         LINE#         366
  C:10F9H         LINE#         368
  C:10F9H         LINE#         369
  C:10FDH         LINE#         370
  C:10FDH         LINE#         371
  C:110AH         LINE#         372
  C:1117H         LINE#         373
  C:1124H         LINE#         374
  C:1131H         LINE#         375
  C:1131H         LINE#         378
  C:1139H         LINE#         379
  C:1139H         LINE#         380
  C:113CH         LINE#         381
  C:113CH         LINE#         382
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  17:20:53  PAGE 11


  C:1141H         LINE#         383
  C:114CH         LINE#         385
  C:115CH         LINE#         386
  C:1165H         LINE#         387
  C:1165H         LINE#         390
  C:1179H         LINE#         391
  C:1186H         LINE#         393
  C:119AH         LINE#         394
  C:11A7H         LINE#         396
  C:11BBH         LINE#         397
  C:11C8H         LINE#         398
  C:11C8H         LINE#         399
  C:11C8H         LINE#         400
  -------         ENDPROC       LCD_PROC
  -------         PROC          MAIN
  C:1A0EH         LINE#         402
  C:1A0EH         LINE#         403
  C:1A0EH         LINE#         405
  C:1A11H         LINE#         406
  C:1A14H         LINE#         409
  C:1A16H         LINE#         412
  C:1A1CH         LINE#         413
  C:1A1CH         LINE#         415
  C:1A1FH         LINE#         416
  C:1A1FH         LINE#         419
  C:1A22H         LINE#         421
  C:1A25H         LINE#         422
  C:1A28H         LINE#         424
  C:1A28H         LINE#         425
  C:1A28H         LINE#         426
  C:1A2BH         LINE#         427
  C:1A2EH         LINE#         428
  C:1A31H         LINE#         429
  C:1A34H         LINE#         430
  -------         ENDPROC       MAIN
  -------         ENDMOD        MAIN

  -------         MODULE        DS18B20
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  C:1B51H         PUBLIC        ds18b20_reset
  D:00A8H         PUBLIC        IE
  C:0003H         PUBLIC        ds18b20_start
  D:00B8H         PUBLIC        IP
  C:1A79H         PUBLIC        ds18b20_read_byte
  C:1B6BH         PUBLIC        ds18b20_init
  C:1AAFH         PUBLIC        ds18b20_read_bit
  C:1982H         PUBLIC        _ds18b20_write_byte
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  C:1754H         PUBLIC        ds18b20_read_temperture
  C:1B42H         PUBLIC        _delay_10us
  B:00B0H.7       PUBLIC        DS18B20_PORT
  D:00C8H         PUBLIC        T2CON
  C:18D6H         PUBLIC        ds18b20_check
  D:00D0H         PUBLIC        PSW
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  17:20:53  PAGE 12


  -------         PROC          _DELAY_10US
  D:0007H         SYMBOL        xms
  -------         DO            
  D:0006H         SYMBOL        i
  -------         ENDDO         
  C:1B42H         LINE#         4
  C:1B42H         LINE#         5
  C:1B42H         LINE#         7
  C:1B48H         LINE#         8
  C:1B48H         LINE#         9
  C:1B4AH         LINE#         10
  C:1B50H         LINE#         12
  -------         ENDPROC       _DELAY_10US
  -------         PROC          DS18B20_RESET
  C:1B51H         LINE#         20
  C:1B51H         LINE#         21
  C:1B51H         LINE#         22
  C:1B53H         LINE#         23
  C:1B58H         LINE#         24
  C:1B5AH         LINE#         25
  -------         ENDPROC       DS18B20_RESET
  -------         PROC          DS18B20_CHECK
  -------         DO            
  D:0004H         SYMBOL        time_temp
  -------         ENDDO         
  C:18D6H         LINE#         34
  C:18D6H         LINE#         35
  C:18D6H         LINE#         36
  C:18D8H         LINE#         38
  C:18E1H         LINE#         39
  C:18E1H         LINE#         40
  C:18E2H         LINE#         41
  C:18E7H         LINE#         42
  C:18E9H         LINE#         43
  C:18F2H         LINE#         44
  C:18F4H         LINE#         45
  C:18FDH         LINE#         46
  C:18FDH         LINE#         47
  C:18FEH         LINE#         48
  C:1903H         LINE#         49
  C:1905H         LINE#         50
  C:190EH         LINE#         51
  C:1910H         LINE#         52
  -------         ENDPROC       DS18B20_CHECK
  -------         PROC          DS18B20_READ_BIT
  -------         DO            
  D:0004H         SYMBOL        dat
  -------         ENDDO         
  C:1AAFH         LINE#         60
  C:1AAFH         LINE#         61
  C:1AAFH         LINE#         62
  C:1AB1H         LINE#         64
  C:1AB3H         LINE#         65
  C:1AB5H         LINE#         66
  C:1AB7H         LINE#         67
  C:1AB9H         LINE#         68
  C:1ABFH         LINE#         69
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  17:20:53  PAGE 13


  C:1AC1H         LINE#         70
  C:1AC6H         LINE#         71
  C:1AC8H         LINE#         72
  -------         ENDPROC       DS18B20_READ_BIT
  -------         PROC          DS18B20_READ_BYTE
  -------         DO            
  D:0003H         SYMBOL        i
  D:0002H         SYMBOL        dat
  D:0007H         SYMBOL        temp
  -------         ENDDO         
  C:1A79H         LINE#         80
  C:1A79H         LINE#         81
  C:1A79H         LINE#         82
  C:1A7BH         LINE#         83
  C:1A7CH         LINE#         84
  C:1A7DH         LINE#         86
  C:1A7DH         LINE#         87
  C:1A7DH         LINE#         88
  C:1A80H         LINE#         89
  C:1A8DH         LINE#         90
  C:1A91H         LINE#         91
  C:1A93H         LINE#         92
  -------         ENDPROC       DS18B20_READ_BYTE
  -------         PROC          L?0035
  -------         ENDPROC       L?0035
  -------         PROC          _DS18B20_WRITE_BYTE
  D:0004H         SYMBOL        dat
  -------         DO            
  D:0003H         SYMBOL        i
  D:0002H         SYMBOL        temp
  -------         ENDDO         
  C:1982H         LINE#         100
  C:1984H         LINE#         101
  C:1984H         LINE#         102
  C:1986H         LINE#         103
  C:1986H         LINE#         105
  C:1986H         LINE#         106
  C:1986H         LINE#         107
  C:198AH         LINE#         108
  C:198EH         LINE#         109
  C:1991H         LINE#         110
  C:1991H         LINE#         111
  C:1993H         LINE#         112
  C:1995H         LINE#         113
  C:1997H         LINE#         114
  C:199CH         LINE#         115
  C:199EH         LINE#         117
  C:199EH         LINE#         118
  C:19A0H         LINE#         119
  C:19A5H         LINE#         120
  C:19A7H         LINE#         121
  C:19A9H         LINE#         122
  C:19A9H         LINE#         123
  C:19ADH         LINE#         124
  -------         ENDPROC       _DS18B20_WRITE_BYTE
  -------         PROC          DS18B20_START
  C:0003H         LINE#         132
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  17:20:53  PAGE 14


  C:0003H         LINE#         133
  C:0003H         LINE#         134
  C:0003H         LINE#         135
  C:0003H         LINE#         136
  C:0006H         LINE#         137
  -------         ENDPROC       DS18B20_START
  -------         PROC          DS18B20_INIT
  C:1B6BH         LINE#         146
  C:1B6BH         LINE#         147
  C:1B6BH         LINE#         148
  C:1B6EH         LINE#         149
  C:1B71H         LINE#         150
  -------         ENDPROC       DS18B20_INIT
  -------         PROC          DS18B20_READ_TEMPERTURE
  -------         DO            
  D:0000H         SYMBOL        temp
  D:0007H         SYMBOL        dath
  D:0001H         SYMBOL        datl
  D:0004H         SYMBOL        value
  -------         ENDDO         
  C:1754H         LINE#         158
  C:1754H         LINE#         159
  C:1754H         LINE#         161
  C:1756H         LINE#         162
  C:1757H         LINE#         163
  C:175BH         LINE#         165
  C:175EH         LINE#         166
  C:175EH         LINE#         167
  C:175EH         LINE#         168
  C:1761H         LINE#         169
  C:1766H         LINE#         171
  C:176BH         LINE#         172
  C:176EH         LINE#         173
  C:177AH         LINE#         175
  C:1781H         LINE#         176
  C:1781H         LINE#         177
  C:1792H         LINE#         178
  C:17A0H         LINE#         179
  C:17A2H         LINE#         181
  C:17A2H         LINE#         182
  C:17BCH         LINE#         183
  C:17BCH         LINE#         184
  C:17C4H         LINE#         185
  -------         ENDPROC       DS18B20_READ_TEMPERTURE
  -------         ENDMOD        DS18B20

  -------         MODULE        LCD9648
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0080H         PUBLIC        P0
  D:0090H         PUBLIC        P1
  D:00A0H         PUBLIC        P2
  D:00B0H         PUBLIC        P3
  C:1AA5H         PUBLIC        _WriteData
  C:15C6H         PUBLIC        _LCD9648_Write16EnCHAR1
  D:00A8H         PUBLIC        IE
  D:00B8H         PUBLIC        IP
  C:1B29H         PUBLIC        _WriteComm
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  17:20:53  PAGE 15


  B:00A0H.7       PUBLIC        RS
  C:17C5H         PUBLIC        LCD9648_Init
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  B:0080H.0       PUBLIC        CS0
  C:000EH         PUBLIC        CN16CHARfont
  C:0250H         PUBLIC        EN16CHARfont
  B:00A0H.5       PUBLIC        SDA
  C:1335H         PUBLIC        _LCD9648_Write16CnCHAR
  C:13F8H         PUBLIC        _LCD9648_Write16EnCHAR
  B:00A0H.6       PUBLIC        SCL
  D:00C8H         PUBLIC        T2CON
  C:1AC9H         PUBLIC        _SendDataSPI
  B:0080H.1       PUBLIC        RST
  C:19E1H         PUBLIC        LCD9648_Clear
  D:00D0H         PUBLIC        PSW
  -------         PROC          _SENDDATASPI
  D:0007H         SYMBOL        dat
  -------         DO            
  D:0006H         SYMBOL        i
  -------         ENDDO         
  C:1AC9H         LINE#         4
  C:1AC9H         LINE#         5
  C:1AC9H         LINE#         8
  C:1ACBH         LINE#         9
  C:1ACBH         LINE#         10
  C:1AD3H         LINE#         11
  C:1AD5H         LINE#         13
  C:1AD9H         LINE#         15
  C:1ADBH         LINE#         16
  C:1ADDH         LINE#         17
  C:1AE1H         LINE#         18
  -------         ENDPROC       _SENDDATASPI
  C:1B23H         SYMBOL        L?0067
  C:1B25H         SYMBOL        L?0068
  -------         PROC          L?0066
  -------         ENDPROC       L?0066
  C:1B23H         SYMBOL        L?0067
  C:1B25H         SYMBOL        L?0068
  -------         PROC          _WRITECOMM
  D:0006H         SYMBOL        i
  C:1B29H         LINE#         20
  C:1B29H         LINE#         21
  C:1B29H         LINE#         23
  C:1B2BH         LINE#         24
  C:1B2DH         LINE#         26
  C:1B30H         LINE#         28
  C:1B32H         LINE#         30
  -------         ENDPROC       _WRITECOMM
  C:1A99H         SYMBOL        L?0070
  -------         PROC          L?0069
  -------         ENDPROC       L?0069
  C:1A99H         SYMBOL        L?0070
  -------         PROC          _WRITEDATA
  D:0006H         SYMBOL        i
  C:1AA5H         LINE#         31
  C:1AA5H         LINE#         32
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  17:20:53  PAGE 16


  C:1AA5H         LINE#         33
  C:1AA7H         LINE#         34
  C:1AA9H         LINE#         36
  C:1AACH         LINE#         38
  C:1AAEH         LINE#         39
  -------         ENDPROC       _WRITEDATA
  -------         PROC          LCD9648_INIT
  -------         DO            
  D:0006H         SYMBOL        i
  -------         ENDDO         
  C:17C5H         LINE#         41
  C:17C5H         LINE#         42
  C:17C5H         LINE#         46
  C:17C7H         LINE#         47
  C:17D5H         LINE#         49
  C:17D7H         LINE#         50
  C:17E5H         LINE#         52
  C:17E7H         LINE#         53
  C:17F5H         LINE#         55
  C:17FCH         LINE#         56
  C:1803H         LINE#         57
  C:180AH         LINE#         58
  C:1811H         LINE#         59
  C:1818H         LINE#         60
  C:181FH         LINE#         61
  C:1826H         LINE#         62
  -------         ENDPROC       LCD9648_INIT
  -------         PROC          LCD9648_CLEAR
  -------         DO            
  D:0005H         SYMBOL        i
  D:0004H         SYMBOL        j
  -------         ENDDO         
  C:19E1H         LINE#         67
  C:19E1H         LINE#         68
  C:19E1H         LINE#         71
  C:19E3H         LINE#         72
  C:19E3H         LINE#         73
  C:19EAH         LINE#         74
  C:19F0H         LINE#         75
  C:19F7H         LINE#         76
  C:19FDH         LINE#         78
  C:19FFH         LINE#         79
  C:19FFH         LINE#         80
  C:1A05H         LINE#         81
  C:1A09H         LINE#         82
  C:1A0DH         LINE#         83
  -------         ENDPROC       LCD9648_CLEAR
  -------         PROC          _LCD9648_WRITE16CNCHAR
  D:0000H         SYMBOL        x
  D:0001H         SYMBOL        y
  D:0001H         SYMBOL        cn
  -------         DO            
  D:0004H         SYMBOL        j
  D:0002H         SYMBOL        x1
  D:0003H         SYMBOL        x2
  D:0005H         SYMBOL        wordNum
  -------         ENDDO         
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  17:20:53  PAGE 17


  C:1335H         LINE#         86
  C:1339H         LINE#         87
  C:1339H         LINE#         91
  C:1340H         LINE#         92
  C:1340H         LINE#         93
  C:1343H         LINE#         94
  C:1343H         LINE#         97
  C:134AH         LINE#         98
  C:134AH         LINE#         99
  C:134DH         LINE#         100
  C:134DH         LINE#         101
  C:1353H         LINE#         103
  C:1359H         LINE#         104
  C:1361H         LINE#         105
  C:1361H         LINE#         108
  C:1368H         LINE#         110
  C:136FH         LINE#         111
  C:1375H         LINE#         113
  C:1378H         LINE#         114
  C:137FH         LINE#         115
  C:1381H         LINE#         116
  C:1381H         LINE#         118
  C:13B4H         LINE#         120
  C:13B4H         LINE#         121
  C:13B5H         LINE#         122
  C:13B5H         LINE#         123
  C:13BAH         LINE#         124
  C:13BAH         LINE#         126
  C:13C1H         LINE#         129
  C:13C4H         LINE#         130
  C:13CBH         LINE#         131
  C:13CBH         LINE#         132
  C:13DBH         LINE#         133
  C:13DFH         LINE#         134
  C:13E5H         LINE#         135
  C:13E5H         LINE#         136
  C:13EBH         LINE#         137
  C:13F2H         LINE#         139
  C:13F5H         LINE#         140
  C:13F7H         LINE#         141
  -------         ENDPROC       _LCD9648_WRITE16CNCHAR
  -------         PROC          _LCD9648_WRITE16ENCHAR
  D:0013H         SYMBOL        x
  D:0014H         SYMBOL        y
  D:0001H         SYMBOL        en
  -------         DO            
  D:0004H         SYMBOL        j
  D:0015H         SYMBOL        x1
  D:0016H         SYMBOL        x2
  D:0005H         SYMBOL        wordNum
  -------         ENDDO         
  C:13F8H         LINE#         143
  C:13FCH         LINE#         144
  C:13FCH         LINE#         148
  C:1403H         LINE#         149
  C:1403H         LINE#         150
  C:1406H         LINE#         151
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  17:20:53  PAGE 18


  C:1406H         LINE#         154
  C:140DH         LINE#         155
  C:140DH         LINE#         156
  C:1410H         LINE#         157
  C:1410H         LINE#         158
  C:1416H         LINE#         160
  C:141CH         LINE#         161
  C:1424H         LINE#         162
  C:1424H         LINE#         165
  C:142BH         LINE#         167
  C:1432H         LINE#         168
  C:1438H         LINE#         170
  C:143DH         LINE#         171
  C:1444H         LINE#         172
  C:1446H         LINE#         173
  C:1446H         LINE#         175
  C:145EH         LINE#         176
  C:145EH         LINE#         177
  C:145FH         LINE#         178
  C:145FH         LINE#         179
  C:1464H         LINE#         180
  C:1464H         LINE#         182
  C:146BH         LINE#         185
  C:1470H         LINE#         186
  C:1477H         LINE#         187
  C:1477H         LINE#         188
  C:1487H         LINE#         189
  C:148BH         LINE#         190
  C:1491H         LINE#         191
  C:1491H         LINE#         192
  C:1497H         LINE#         193
  C:149EH         LINE#         195
  C:14A1H         LINE#         196
  C:14A3H         LINE#         197
  -------         ENDPROC       _LCD9648_WRITE16ENCHAR
  -------         PROC          _LCD9648_WRITE16ENCHAR1
  D:0001H         SYMBOL        x
  D:0005H         SYMBOL        y
  D:0003H         SYMBOL        en
  -------         DO            
  D:0002H         SYMBOL        j
  D:000EH         SYMBOL        x1
  D:000FH         SYMBOL        x2
  D:0004H         SYMBOL        wordNum
  -------         ENDDO         
  C:15C6H         LINE#         198
  C:15C8H         LINE#         199
  C:15C8H         LINE#         203
  C:15CEH         LINE#         204
  C:15CEH         LINE#         205
  C:15D1H         LINE#         206
  C:15D1H         LINE#         209
  C:15D7H         LINE#         210
  C:15D7H         LINE#         211
  C:15DAH         LINE#         212
  C:15DAH         LINE#         213
  C:15DEH         LINE#         215
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  17:20:53  PAGE 19


  C:15E4H         LINE#         217
  C:15E4H         LINE#         220
  C:15EBH         LINE#         222
  C:15F1H         LINE#         223
  C:15F6H         LINE#         225
  C:15FBH         LINE#         226
  C:1602H         LINE#         227
  C:1604H         LINE#         228
  C:1604H         LINE#         230
  C:1618H         LINE#         231
  C:1618H         LINE#         232
  C:1619H         LINE#         233
  C:1619H         LINE#         234
  C:161EH         LINE#         235
  C:161EH         LINE#         237
  C:1624H         LINE#         240
  C:1629H         LINE#         241
  C:1630H         LINE#         242
  C:1630H         LINE#         243
  C:1645H         LINE#         244
  C:1649H         LINE#         245
  C:164DH         LINE#         246
  C:164DH         LINE#         247
  C:1651H         LINE#         248
  C:1651H         LINE#         250
  C:1651H         LINE#         251
  C:1653H         LINE#         252
  -------         ENDPROC       _LCD9648_WRITE16ENCHAR1
  -------         ENDMOD        LCD9648

  -------         MODULE        KEY
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  C:1AE2H         PUBLIC        Key_Read
  D:00A8H         PUBLIC        IE
  D:00B8H         PUBLIC        IP
  B:0090H.0       PUBLIC        k1
  B:0090H.1       PUBLIC        k2
  B:0090H.2       PUBLIC        k3
  B:0090H.3       PUBLIC        k4
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  D:00C8H         PUBLIC        T2CON
  D:00D0H         PUBLIC        PSW
  -------         PROC          KEY_READ
  -------         DO            
  D:0007H         SYMBOL        temp
  -------         ENDDO         
  C:1AE2H         LINE#         4
  C:1AE2H         LINE#         5
  C:1AE2H         LINE#         6
  C:1AE4H         LINE#         8
  C:1AE8H         LINE#         9
  C:1AEDH         LINE#         10
  C:1AF2H         LINE#         11
  C:1AF7H         LINE#         12
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  17:20:53  PAGE 20


  C:1AF7H         LINE#         13
  -------         ENDPROC       KEY_READ
  -------         ENDMOD        KEY

  -------         MODULE        RTC3085
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  D:00A8H         PUBLIC        IE
  C:1911H         PUBLIC        _Ds1302Write
  C:1AF8H         PUBLIC        Ds1302ReadTime
  C:1A36H         PUBLIC        Ds1302Init
  D:00B8H         PUBLIC        IP
  B:00B0H.6       PUBLIC        SCLK
  B:00B0H.4       PUBLIC        DSIO
  D:0008H         PUBLIC        TIME
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  C:1B65H         PUBLIC        WRITE_RTC_ADDR
  C:1B5FH         PUBLIC        READ_RTC_ADDR
  D:00C8H         PUBLIC        T2CON
  B:00B0H.5       PUBLIC        RST
  D:00D0H         PUBLIC        PSW
  C:188CH         PUBLIC        _Ds1302Read
  -------         PROC          _DS1302WRITE
  D:0007H         SYMBOL        addr
  D:0005H         SYMBOL        dat
  -------         DO            
  D:0006H         SYMBOL        n
  -------         ENDDO         
  C:1911H         LINE#         25
  C:1911H         LINE#         26
  C:1911H         LINE#         28
  C:1913H         LINE#         29
  C:1914H         LINE#         31
  C:1916H         LINE#         32
  C:1917H         LINE#         33
  C:1919H         LINE#         34
  C:191AH         LINE#         36
  C:191CH         LINE#         37
  C:191CH         LINE#         38
  C:1920H         LINE#         39
  C:1924H         LINE#         40
  C:1926H         LINE#         41
  C:1927H         LINE#         42
  C:1929H         LINE#         43
  C:192AH         LINE#         44
  C:192EH         LINE#         45
  C:1930H         LINE#         46
  C:1930H         LINE#         47
  C:1934H         LINE#         48
  C:1938H         LINE#         49
  C:193AH         LINE#         50
  C:193BH         LINE#         51
  C:193DH         LINE#         52
  C:193EH         LINE#         53
  C:1942H         LINE#         55
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  17:20:53  PAGE 21


  C:1944H         LINE#         56
  C:1945H         LINE#         57
  -------         ENDPROC       _DS1302WRITE
  -------         PROC          _DS1302READ
  D:0007H         SYMBOL        addr
  -------         DO            
  D:0005H         SYMBOL        n
  D:0006H         SYMBOL        dat
  D:0007H         SYMBOL        dat1
  -------         ENDDO         
  C:188CH         LINE#         66
  C:188CH         LINE#         67
  C:188CH         LINE#         69
  C:188EH         LINE#         70
  C:188FH         LINE#         72
  C:1891H         LINE#         73
  C:1892H         LINE#         74
  C:1894H         LINE#         75
  C:1895H         LINE#         77
  C:1897H         LINE#         78
  C:1897H         LINE#         79
  C:189BH         LINE#         80
  C:189FH         LINE#         81
  C:18A1H         LINE#         82
  C:18A2H         LINE#         83
  C:18A4H         LINE#         84
  C:18A5H         LINE#         85
  C:18A9H         LINE#         86
  C:18AAH         LINE#         87
  C:18ACH         LINE#         88
  C:18ACH         LINE#         89
  C:18B1H         LINE#         90
  C:18BDH         LINE#         91
  C:18BFH         LINE#         92
  C:18C0H         LINE#         93
  C:18C2H         LINE#         94
  C:18C3H         LINE#         95
  C:18C7H         LINE#         97
  C:18C9H         LINE#         98
  C:18CAH         LINE#         99
  C:18CCH         LINE#         100
  C:18CDH         LINE#         101
  C:18CFH         LINE#         102
  C:18D0H         LINE#         103
  C:18D2H         LINE#         104
  C:18D3H         LINE#         105
  C:18D5H         LINE#         106
  -------         ENDPROC       _DS1302READ
  -------         PROC          DS1302INIT
  -------         DO            
  D:0004H         SYMBOL        n
  -------         ENDDO         
  C:1A36H         LINE#         115
  C:1A36H         LINE#         116
  C:1A36H         LINE#         118
  C:1A3DH         LINE#         119
  C:1A3FH         LINE#         120
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  17:20:53  PAGE 22


  C:1A3FH         LINE#         121
  C:1A4EH         LINE#         122
  C:1A52H         LINE#         123
  -------         ENDPROC       DS1302INIT
  -------         PROC          DS1302READTIME
  -------         DO            
  D:0003H         SYMBOL        n
  -------         ENDDO         
  C:1AF8H         LINE#         133
  C:1AF8H         LINE#         134
  C:1AF8H         LINE#         136
  C:1AFAH         LINE#         137
  C:1AFAH         LINE#         138
  C:1B09H         LINE#         139
  C:1B0DH         LINE#         141
  -------         ENDPROC       DS1302READTIME
  -------         ENDMOD        RTC3085

  -------         MODULE        ?C?FPADD
  C:08A6H         PUBLIC        ?C?FPADD
  C:08A2H         PUBLIC        ?C?FPSUB
  -------         ENDMOD        ?C?FPADD

  -------         MODULE        ?C?FPMUL
  C:0997H         PUBLIC        ?C?FPMUL
  -------         ENDMOD        ?C?FPMUL

  -------         MODULE        ?C?FPCMP
  C:0A9FH         PUBLIC        ?C?FPCMP
  C:0A9DH         PUBLIC        ?C?FPCMP3
  -------         ENDMOD        ?C?FPCMP

  -------         MODULE        ?C?FCAST
  C:0B20H         PUBLIC        ?C?FCASTC
  C:0B1BH         PUBLIC        ?C?FCASTI
  C:0B16H         PUBLIC        ?C?FCASTL
  -------         ENDMOD        ?C?FCAST

  -------         MODULE        ?C?CASTF
  C:0B54H         PUBLIC        ?C?CASTF
  -------         ENDMOD        ?C?CASTF

  -------         MODULE        ?C?CLDPTR
  C:0BD5H         PUBLIC        ?C?CLDPTR
  -------         ENDMOD        ?C?CLDPTR

  -------         MODULE        ?C?CLDOPTR
  C:0BEEH         PUBLIC        ?C?CLDOPTR
  -------         ENDMOD        ?C?CLDOPTR

  -------         MODULE        ?C?CSTPTR
  C:0C1BH         PUBLIC        ?C?CSTPTR
  -------         ENDMOD        ?C?CSTPTR

  -------         MODULE        ?C?CSTOPTR
  C:0C2DH         PUBLIC        ?C?CSTOPTR
  -------         ENDMOD        ?C?CSTOPTR
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  17:20:53  PAGE 23



  -------         MODULE        ?C?LNEG
  C:0C4FH         PUBLIC        ?C?LNEG
  -------         ENDMOD        ?C?LNEG

  -------         MODULE        ?C?LLDIDATA
  C:0C5DH         PUBLIC        ?C?LLDIDATA
  -------         ENDMOD        ?C?LLDIDATA

  -------         MODULE        ?C?LSTIDATA
  C:0C69H         PUBLIC        ?C?LSTIDATA
  -------         ENDMOD        ?C?LSTIDATA

  -------         MODULE        ?C?LSTKIDATA
  C:0C75H         PUBLIC        ?C?LSTKIDATA
  -------         ENDMOD        ?C?LSTKIDATA

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?_BCD_TO_DEC?MAIN

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?_LCD9648_WRITE16ENCHAR1?LCD9648

*** ERROR L107: ADDRESS SPACE OVERFLOW
    SPACE:   DATA    
    SEGMENT: _DATA_GROUP_
    LENGTH:  001CH

Program Size: data=114.1 xdata=0 code=7026
LINK/LOCATE RUN COMPLETE.  2 WARNING(S),  1 ERROR(S)
