BL51 BANKED LINKER/LOCATER V6.22                                                        07/08/2025  16:59:15  PAGE 1


BL51 BANKED LINKER/LOCATER V6.22, INVOKED BY:
E:\KEIL\C51\BIN\BL51.EXE .\Objects\main.obj, .\Objects\ds18b20.obj, .\Objects\lcd9648.obj, .\Objects\Key.obj, .\Objects\
>> rtc3085.obj TO .\Objects\Project PRINT (.\Listings\Project.m51) RAMSIZE (256)


MEMORY MODEL: SMALL WITH FLOATING POINT ARITHMETIC


INPUT MODULES INCLUDED:
  .\Objects\main.obj (MAIN)
  .\Objects\ds18b20.obj (DS18B20)
  .\Objects\lcd9648.obj (LCD9648)
  .\Objects\Key.obj (KEY)
  .\Objects\rtc3085.obj (RTC3085)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?FPADD)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?FPMUL)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?FPCMP)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?FCAST)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?CASTF)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?FPGETOPN)
  E:\KEIL\C51\LIB\C51S.LIB (?C_STARTUP)
  E:\KEIL\C51\LIB\C51S.LIB (?C?CLDPTR)
  E:\KEIL\C51\LIB\C51S.LIB (?C?CLDOPTR)
  E:\KEIL\C51\LIB\C51S.LIB (?C?CSTPTR)
  E:\KEIL\C51\LIB\C51S.LIB (?C?CSTOPTR)
  E:\KEIL\C51\LIB\C51S.LIB (?C?LNEG)
  E:\KEIL\C51\LIB\C51S.LIB (?C?LLDIDATA)
  E:\KEIL\C51\LIB\C51S.LIB (?C?LSTIDATA)
  E:\KEIL\C51\LIB\C51S.LIB (?C?LSTKIDATA)
  E:\KEIL\C51\LIB\C51S.LIB (?C_INIT)


LINK MAP OF MODULE:  .\Objects\Project (MAIN)


            TYPE    BASE      LENGTH    RELOCATION   SEGMENT NAME
            -----------------------------------------------------

            * * * * * * *   D A T A   M E M O R Y   * * * * * * *
            REG     0000H     0008H     ABSOLUTE     "REG BANK 0"
            DATA    0008H     0006H     UNIT         ?DT?DS18B20_READ_TEMPERTURE?DS18B20
            DATA    000EH     0006H     UNIT         ?DT?RTC3085
            DATA    0014H     0002H     UNIT         ?DT?_LCD9648_WRITE16ENCHAR1?LCD9648
                    0016H     000AH                  *** GAP ***
            BIT     0020H.0   0000H.1   UNIT         ?BI?MAIN
                    0020H.1   0000H.7                *** GAP ***
            DATA    0021H     0041H     UNIT         ?DT?MAIN
            DATA    0062H     001CH     UNIT         _DATA_GROUP_
            IDATA   007EH     0001H     UNIT         ?STACK

            * * * * * * *   C O D E   M E M O R Y   * * * * * * *
            CODE    0000H     0003H     ABSOLUTE     
            CODE    0003H     0008H     UNIT         ?PR?DS18B20_START?DS18B20
            CODE    000BH     0003H     ABSOLUTE     
            CODE    000EH     0891H     UNIT         ?CO?LCD9648
            CODE    089FH     03EFH     UNIT         ?C?LIB_CODE
            CODE    0C8EH     02C4H     UNIT         ?PR?KEY_PROC?MAIN
            CODE    0F52H     0277H     UNIT         ?PR?LCD_PROC?MAIN
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  16:59:15  PAGE 2


            CODE    11C9H     016CH     UNIT         ?PR?LCD9648_INIT_PROC?MAIN
            CODE    1335H     00C3H     UNIT         ?PR?_LCD9648_WRITE16CNCHAR?LCD9648
            CODE    13F8H     00ACH     UNIT         ?PR?_LCD9648_WRITE16ENCHAR?LCD9648
            CODE    14A4H     0091H     UNIT         ?PR?_FLOAT_TO_STRING?MAIN
            CODE    1535H     008EH     UNIT         ?PR?_LCD9648_WRITE16ENCHAR1?LCD9648
            CODE    15C3H     008CH     UNIT         ?C_C51STARTUP
            CODE    164FH     0084H     UNIT         ?CO?MAIN
            CODE    16D3H     0074H     UNIT         ?PR?_TIME_TO_STRING?MAIN
            CODE    1747H     0071H     UNIT         ?PR?DS18B20_READ_TEMPERTURE?DS18B20
            CODE    17B8H     0068H     UNIT         ?PR?LCD9648_INIT?LCD9648
            CODE    1820H     0056H     UNIT         ?C_INITSEG
            CODE    1876H     004AH     UNIT         ?PR?_DS1302READ?RTC3085
            CODE    18C0H     003BH     UNIT         ?PR?DS18B20_CHECK?DS18B20
            CODE    18FBH     0035H     UNIT         ?PR?_DS1302WRITE?RTC3085
            CODE    1930H     0034H     UNIT         ?PR?_DS18B20_WRITE_BYTE?DS18B20
            CODE    1964H     0033H     UNIT         ?PR?TIMER0_ISR?MAIN
            CODE    1997H     002DH     UNIT         ?PR?LCD9648_CLEAR?LCD9648
            CODE    19C4H     0023H     UNIT         ?PR?DS1302INIT?RTC3085
            CODE    19E7H     0020H     UNIT         ?PR?LCD_INIT_TEST?MAIN
            CODE    1A07H     001BH     UNIT         ?PR?DS18B20_READ_BYTE?DS18B20
            CODE    1A22H     001BH     UNIT         ?PR?_WRITEDATA?LCD9648
            CODE    1A3DH     001AH     UNIT         ?PR?MAIN?MAIN
            CODE    1A57H     001AH     UNIT         ?PR?DS18B20_READ_BIT?DS18B20
            CODE    1A71H     0019H     UNIT         ?PR?_SENDDATASPI?LCD9648
            CODE    1A8AH     0016H     UNIT         ?PR?KEY_READ?KEY
            CODE    1AA0H     0016H     UNIT         ?PR?DS1302READTIME?RTC3085
            CODE    1AB6H     0013H     UNIT         ?PR?TIMER0_INIT?MAIN
            CODE    1AC9H     0012H     UNIT         ?PR?_WRITECOMM?LCD9648
            CODE    1ADBH     000FH     UNIT         ?PR?_BCD_TO_DEC?MAIN
            CODE    1AEAH     000FH     UNIT         ?PR?_DELAY_10US?DS18B20
            CODE    1AF9H     000EH     UNIT         ?PR?DS18B20_RESET?DS18B20
            CODE    1B07H     000CH     UNIT         ?CO?RTC3085
            CODE    1B13H     0007H     UNIT         ?PR?DS18B20_INIT?DS18B20



OVERLAY MAP OF MODULE:   .\Objects\Project (MAIN)


SEGMENT                                       DATA_GROUP 
  +--> CALLED SEGMENT                       START    LENGTH
-----------------------------------------------------------
?C_C51STARTUP                               -----    -----
  +--> ?PR?MAIN?MAIN
  +--> ?C_INITSEG

?PR?MAIN?MAIN                               -----    -----
  +--> ?PR?DS1302INIT?RTC3085
  +--> ?PR?LCD9648_INIT_PROC?MAIN
  +--> ?PR?TIMER0_INIT?MAIN
  +--> ?PR?LCD_INIT_TEST?MAIN
  +--> ?PR?KEY_PROC?MAIN
  +--> ?PR?LCD_PROC?MAIN

?PR?DS1302INIT?RTC3085                      -----    -----
  +--> ?PR?_DS1302WRITE?RTC3085
  +--> ?CO?RTC3085
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  16:59:15  PAGE 3



?PR?LCD9648_INIT_PROC?MAIN                  -----    -----
  +--> ?PR?LCD9648_INIT?LCD9648
  +--> ?PR?LCD9648_CLEAR?LCD9648
  +--> ?CO?MAIN
  +--> ?PR?_LCD9648_WRITE16CNCHAR?LCD9648
  +--> ?PR?_LCD9648_WRITE16ENCHAR?LCD9648

?PR?LCD9648_INIT?LCD9648                    -----    -----
  +--> ?PR?_WRITECOMM?LCD9648

?PR?_WRITECOMM?LCD9648                      -----    -----
  +--> ?PR?_SENDDATASPI?LCD9648

?PR?LCD9648_CLEAR?LCD9648                   -----    -----
  +--> ?PR?_WRITECOMM?LCD9648
  +--> ?PR?_WRITEDATA?LCD9648

?PR?_WRITEDATA?LCD9648                      -----    -----
  +--> ?PR?_SENDDATASPI?LCD9648

?PR?_LCD9648_WRITE16CNCHAR?LCD9648          0062H    0004H
  +--> ?PR?_WRITECOMM?LCD9648
  +--> ?CO?LCD9648
  +--> ?PR?_WRITEDATA?LCD9648

?PR?_LCD9648_WRITE16ENCHAR?LCD9648          0075H    0004H
  +--> ?PR?_WRITECOMM?LCD9648
  +--> ?CO?LCD9648
  +--> ?PR?_WRITEDATA?LCD9648

?PR?LCD_INIT_TEST?MAIN                      -----    -----
  +--> ?PR?LCD9648_INIT_PROC?MAIN

?PR?KEY_PROC?MAIN                           -----    -----
  +--> ?PR?KEY_READ?KEY

?PR?LCD_PROC?MAIN                           0062H    0013H
  +--> ?PR?LCD9648_CLEAR?LCD9648
  +--> ?CO?MAIN
  +--> ?PR?_LCD9648_WRITE16ENCHAR?LCD9648
  +--> ?PR?_FLOAT_TO_STRING?MAIN
  +--> ?PR?_TIME_TO_STRING?MAIN

?PR?_FLOAT_TO_STRING?MAIN                   0075H    0009H

?PR?_TIME_TO_STRING?MAIN                    0075H    0003H
  +--> ?PR?DS1302READTIME?RTC3085

?PR?DS1302READTIME?RTC3085                  -----    -----
  +--> ?CO?RTC3085
  +--> ?PR?_DS1302READ?RTC3085



SYMBOL TABLE OF MODULE:  .\Objects\Project (MAIN)

BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  16:59:15  PAGE 4


  VALUE           TYPE          NAME
  ----------------------------------

  -------         MODULE        MAIN
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0080H         PUBLIC        P0
  D:0090H         PUBLIC        P1
  D:00A0H         PUBLIC        P2
  D:00B0H         PUBLIC        P3
  C:1964H         PUBLIC        Timer0_ISR
  B:00A8H.7       PUBLIC        EA
  D:00A8H         PUBLIC        IE
  C:16D3H         PUBLIC        _Time_To_String
  D:0021H         PUBLIC        Last_LCD_Page_Mode
  D:0022H         PUBLIC        Temperature
  D:0023H         PUBLIC        Display_Buffer
  D:00B8H         PUBLIC        IP
  D:0033H         PUBLIC        LCD_Page_Mode
  C:11C9H         PUBLIC        LCD9648_Init_Proc
  B:0020H.0       PUBLIC        LCD_Init_End_Flag
  C:0C8EH         PUBLIC        Key_Proc
  D:0034H         PUBLIC        Key_Down
  C:1A3DH         PUBLIC        main
  C:1AB6H         PUBLIC        Timer0_Init
  D:0035H         PUBLIC        Key_Old
  D:0036H         PUBLIC        Key_Slow_Down
  D:0037H         PUBLIC        Key_Val
  D:0038H         PUBLIC        Last_Display_Page
  D:0039H         PUBLIC        Last_LCD_Mode
  D:003AH         PUBLIC        LCD_Init_Mode
  D:0098H         PUBLIC        SCON
  D:0089H         PUBLIC        TMOD
  D:0088H         PUBLIC        TCON
  D:003BH         PUBLIC        Last_Setting_Page
  D:003CH         PUBLIC        Display_Page
  D:003DH         PUBLIC        Setting_Index
  D:003EH         PUBLIC        Timer0_count
  D:0040H         PUBLIC        Setting_Page
  B:00A8H.1       PUBLIC        ET0
  C:0F52H         PUBLIC        LCD_Proc
  D:008CH         PUBLIC        TH0
  C:19E7H         PUBLIC        LCD_Init_Test
  D:0041H         PUBLIC        Key_Up
  D:008AH         PUBLIC        TL0
  D:0042H         PUBLIC        Setting_Disp
  D:004EH         PUBLIC        Last_State_Index
  B:0088H.4       PUBLIC        TR0
  D:004FH         PUBLIC        Time_Flag_Count
  C:1ADBH         PUBLIC        _BCD_To_Dec
  D:0050H         PUBLIC        State_Index
  D:00C8H         PUBLIC        T2CON
  D:0051H         PUBLIC        Time_Flag
  D:0052H         PUBLIC        State_Disp
  C:14B5H         PUBLIC        _Float_To_String
  D:00D0H         PUBLIC        PSW
  -------         PROC          _BCD_TO_DEC
  D:0007H         SYMBOL        bcd
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  16:59:15  PAGE 5


  C:1ADBH         LINE#         35
  -------         ENDPROC       _BCD_TO_DEC
  -------         PROC          _TIME_TO_STRING
  D:0075H         SYMBOL        time_str
  C:16D3H         LINE#         42
  C:16D9H         LINE#         43
  C:16D9H         LINE#         44
  C:16DCH         LINE#         47
  C:16ECH         LINE#         48
  C:16FEH         LINE#         49
  C:1706H         LINE#         50
  C:1713H         LINE#         51
  C:171FH         LINE#         52
  C:1727H         LINE#         53
  C:1734H         LINE#         54
  C:1740H         LINE#         55
  -------         ENDPROC       _TIME_TO_STRING
  -------         PROC          KEY_PROC
  C:0C8EH         LINE#         58
  C:0C8EH         LINE#         59
  C:0C8EH         LINE#         60
  C:0C95H         LINE#         61
  C:0C98H         LINE#         63
  C:0C9DH         LINE#         64
  C:0CA6H         LINE#         65
  C:0CACH         LINE#         66
  C:0CAFH         LINE#         68
  C:0CCBH         LINE#         69
  C:0CCBH         LINE#         70
  C:0CCBH         LINE#         71
  C:0CD4H         LINE#         72
  C:0CD4H         LINE#         73
  C:0CDFH         LINE#         74
  C:0CDFH         LINE#         75
  C:0CFCH         LINE#         76
  C:0D28H         LINE#         77
  C:0D5CH         LINE#         78
  C:0D5DH         LINE#         79
  C:0D66H         LINE#         80
  C:0D66H         LINE#         81
  C:0D84H         LINE#         82
  C:0DAFH         LINE#         83
  C:0DAFH         LINE#         84
  C:0DB0H         LINE#         85
  C:0DB9H         LINE#         86
  C:0DB9H         LINE#         87
  C:0DD7H         LINE#         88
  C:0E03H         LINE#         89
  C:0E03H         LINE#         90
  C:0E04H         LINE#         92
  C:0E04H         LINE#         93
  C:0E0DH         LINE#         94
  C:0E0DH         LINE#         95
  C:0E18H         LINE#         96
  C:0E18H         LINE#         97
  C:0E35H         LINE#         98
  C:0E63H         LINE#         99
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  16:59:15  PAGE 6


  C:0E99H         LINE#         100
  C:0E9AH         LINE#         101
  C:0EA3H         LINE#         102
  C:0EA3H         LINE#         103
  C:0EC1H         LINE#         104
  C:0EE1H         LINE#         105
  C:0EE1H         LINE#         106
  C:0EE3H         LINE#         107
  C:0EE9H         LINE#         108
  C:0EE9H         LINE#         109
  C:0F07H         LINE#         110
  C:0F2FH         LINE#         111
  C:0F2FH         LINE#         112
  C:0F30H         LINE#         114
  C:0F30H         LINE#         115
  C:0F3AH         LINE#         116
  C:0F3AH         LINE#         117
  C:0F3CH         LINE#         119
  C:0F3CH         LINE#         120
  C:0F47H         LINE#         121
  C:0F47H         LINE#         122
  C:0F51H         LINE#         123
  C:0F51H         LINE#         124
  C:0F51H         LINE#         125
  C:0F51H         LINE#         126
  -------         ENDPROC       KEY_PROC
  C:14ACH         SYMBOL        L?0090
  -------         PROC          L?0089
  -------         ENDPROC       L?0089
  C:14ACH         SYMBOL        L?0090
  -------         PROC          _FLOAT_TO_STRING
  D:0075H         SYMBOL        value
  D:0079H         SYMBOL        str
  D:007CH         SYMBOL        unit
  -------         DO            
  D:007DH         SYMBOL        integer_part
  D:0007H         SYMBOL        decimal_part
  -------         ENDDO         
  C:14B5H         LINE#         130
  C:14C3H         LINE#         131
  C:14C3H         LINE#         132
  C:14C8H         LINE#         133
  C:14EEH         LINE#         135
  C:14FFH         LINE#         136
  C:1515H         LINE#         137
  C:151DH         LINE#         138
  C:1526H         LINE#         139
  C:152EH         LINE#         140
  -------         ENDPROC       _FLOAT_TO_STRING
  -------         PROC          TIMER0_INIT
  C:1AB6H         LINE#         147
  C:1AB6H         LINE#         148
  C:1AB6H         LINE#         149
  C:1AB9H         LINE#         150
  C:1ABCH         LINE#         151
  C:1ABFH         LINE#         152
  C:1AC2H         LINE#         153
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  16:59:15  PAGE 7


  C:1AC4H         LINE#         154
  C:1AC6H         LINE#         155
  C:1AC8H         LINE#         156
  -------         ENDPROC       TIMER0_INIT
  -------         PROC          TIMER0_ISR
  C:1964H         LINE#         162
  C:1968H         LINE#         164
  C:196BH         LINE#         165
  C:196EH         LINE#         167
  C:1976H         LINE#         169
  C:197FH         LINE#         170
  C:197FH         LINE#         171
  C:1982H         LINE#         172
  C:1988H         LINE#         174
  C:1988H         LINE#         176
  C:1992H         LINE#         177
  -------         ENDPROC       TIMER0_ISR
  -------         PROC          LCD9648_INIT_PROC
  C:11C9H         LINE#         178
  C:11C9H         LINE#         179
  C:11C9H         LINE#         180
  C:11DFH         LINE#         181
  C:11DFH         LINE#         182
  C:11DFH         LINE#         183
  C:11E2H         LINE#         184
  C:11E5H         LINE#         186
  C:11F1H         LINE#         187
  C:11FEH         LINE#         188
  C:120BH         LINE#         190
  C:1218H         LINE#         192
  C:1225H         LINE#         193
  C:1232H         LINE#         194
  C:123FH         LINE#         195
  C:124CH         LINE#         196
  C:1259H         LINE#         197
  C:1259H         LINE#         199
  C:1259H         LINE#         200
  C:125CH         LINE#         201
  C:125FH         LINE#         203
  C:126BH         LINE#         204
  C:1278H         LINE#         205
  C:1285H         LINE#         206
  C:1292H         LINE#         207
  C:129FH         LINE#         208
  C:12ACH         LINE#         210
  C:12B9H         LINE#         211
  C:12C6H         LINE#         212
  C:12D3H         LINE#         213
  C:12E0H         LINE#         214
  C:12EDH         LINE#         215
  C:12FAH         LINE#         217
  C:1307H         LINE#         218
  C:1314H         LINE#         219
  C:1321H         LINE#         220
  C:132EH         LINE#         221
  C:132EH         LINE#         223
  C:132EH         LINE#         224
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  16:59:15  PAGE 8


  C:1331H         LINE#         225
  C:1334H         LINE#         226
  C:1334H         LINE#         228
  C:1334H         LINE#         230
  C:1334H         LINE#         231
  C:1334H         LINE#         232
  -------         ENDPROC       LCD9648_INIT_PROC
  -------         PROC          LCD_INIT_TEST
  C:19E7H         LINE#         234
  C:19E7H         LINE#         235
  C:19E7H         LINE#         236
  C:19EBH         LINE#         237
  C:19EBH         LINE#         238
  C:19EEH         LINE#         239
  C:19F0H         LINE#         240
  C:19F2H         LINE#         241
  C:19F9H         LINE#         242
  C:19F9H         LINE#         243
  C:19FCH         LINE#         244
  C:19FCH         LINE#         245
  C:1A01H         LINE#         246
  C:1A06H         LINE#         247
  C:1A06H         LINE#         249
  -------         ENDPROC       LCD_INIT_TEST
  -------         PROC          LCD_PROC
  -------         DO            
  D:0062H         SYMBOL        temp_str
  D:0072H         SYMBOL        i
  D:0073H         SYMBOL        need_clear
  D:0074H         SYMBOL        need_update_pointer
  -------         ENDDO         
  C:0F52H         LINE#         251
  C:0F52H         LINE#         252
  C:0F52H         LINE#         255
  C:0F55H         LINE#         256
  C:0F57H         LINE#         258
  C:0F5DH         LINE#         261
  C:0F63H         LINE#         262
  C:0F63H         LINE#         263
  C:0F66H         LINE#         264
  C:0F69H         LINE#         265
  C:0F6CH         LINE#         266
  C:0F6CH         LINE#         269
  C:0F72H         LINE#         270
  C:0F72H         LINE#         271
  C:0F75H         LINE#         272
  C:0F78H         LINE#         273
  C:0F78H         LINE#         275
  C:0F93H         LINE#         276
  C:0F93H         LINE#         277
  C:0F93H         LINE#         278
  C:0F97H         LINE#         279
  C:0F97H         LINE#         280
  C:0FA4H         LINE#         281
  C:0FB0H         LINE#         282
  C:0FBDH         LINE#         283
  C:0FCAH         LINE#         284
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  16:59:15  PAGE 9


  C:0FCAH         LINE#         287
  C:0FCDH         LINE#         288
  C:0FDAH         LINE#         290
  C:0FE5H         LINE#         291
  C:0FF2H         LINE#         293
  C:1006H         LINE#         294
  C:100EH         LINE#         295
  C:1010H         LINE#         297
  C:1010H         LINE#         298
  C:1014H         LINE#         299
  C:1014H         LINE#         300
  C:1021H         LINE#         301
  C:102EH         LINE#         302
  C:102EH         LINE#         305
  C:1037H         LINE#         306
  C:1041H         LINE#         307
  C:1044H         LINE#         309
  C:1044H         LINE#         310
  C:1048H         LINE#         311
  C:1048H         LINE#         312
  C:1055H         LINE#         313
  C:1062H         LINE#         314
  C:106FH         LINE#         315
  C:107CH         LINE#         316
  C:107CH         LINE#         319
  C:1084H         LINE#         320
  C:1084H         LINE#         321
  C:1087H         LINE#         322
  C:1087H         LINE#         323
  C:108CH         LINE#         324
  C:1097H         LINE#         326
  C:10A7H         LINE#         327
  C:10B0H         LINE#         328
  C:10B0H         LINE#         331
  C:10B3H         LINE#         332
  C:10C0H         LINE#         334
  C:10CBH         LINE#         335
  C:10D8H         LINE#         337
  C:10ECH         LINE#         338
  C:10F6H         LINE#         339
  C:10F9H         LINE#         341
  C:10F9H         LINE#         342
  C:10FDH         LINE#         343
  C:10FDH         LINE#         344
  C:110AH         LINE#         345
  C:1117H         LINE#         346
  C:1124H         LINE#         347
  C:1131H         LINE#         348
  C:1131H         LINE#         351
  C:1139H         LINE#         352
  C:1139H         LINE#         353
  C:113CH         LINE#         354
  C:113CH         LINE#         355
  C:1141H         LINE#         356
  C:114CH         LINE#         358
  C:115CH         LINE#         359
  C:1165H         LINE#         360
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  16:59:15  PAGE 10


  C:1165H         LINE#         363
  C:1179H         LINE#         364
  C:1186H         LINE#         366
  C:119AH         LINE#         367
  C:11A7H         LINE#         369
  C:11BBH         LINE#         370
  C:11C8H         LINE#         371
  C:11C8H         LINE#         372
  C:11C8H         LINE#         373
  -------         ENDPROC       LCD_PROC
  -------         PROC          MAIN
  C:1A3DH         LINE#         375
  C:1A3DH         LINE#         376
  C:1A3DH         LINE#         378
  C:1A40H         LINE#         379
  C:1A43H         LINE#         382
  C:1A46H         LINE#         384
  C:1A49H         LINE#         385
  C:1A4CH         LINE#         387
  C:1A4CH         LINE#         388
  C:1A4CH         LINE#         389
  C:1A4FH         LINE#         390
  C:1A52H         LINE#         391
  C:1A55H         LINE#         392
  -------         ENDPROC       MAIN
  -------         ENDMOD        MAIN

  -------         MODULE        DS18B20
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  C:1AF9H         PUBLIC        ds18b20_reset
  D:00A8H         PUBLIC        IE
  C:0003H         PUBLIC        ds18b20_start
  D:00B8H         PUBLIC        IP
  C:1A07H         PUBLIC        ds18b20_read_byte
  C:1B13H         PUBLIC        ds18b20_init
  C:1A57H         PUBLIC        ds18b20_read_bit
  C:1938H         PUBLIC        _ds18b20_write_byte
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  C:1747H         PUBLIC        ds18b20_read_temperture
  C:1AEAH         PUBLIC        _delay_10us
  B:00B0H.7       PUBLIC        DS18B20_PORT
  D:00C8H         PUBLIC        T2CON
  C:18C0H         PUBLIC        ds18b20_check
  D:00D0H         PUBLIC        PSW
  -------         PROC          _DELAY_10US
  D:0007H         SYMBOL        xms
  -------         DO            
  D:0006H         SYMBOL        i
  -------         ENDDO         
  C:1AEAH         LINE#         4
  C:1AEAH         LINE#         5
  C:1AEAH         LINE#         7
  C:1AF0H         LINE#         8
  C:1AF0H         LINE#         9
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  16:59:15  PAGE 11


  C:1AF2H         LINE#         10
  C:1AF8H         LINE#         12
  -------         ENDPROC       _DELAY_10US
  -------         PROC          DS18B20_RESET
  C:1AF9H         LINE#         20
  C:1AF9H         LINE#         21
  C:1AF9H         LINE#         22
  C:1AFBH         LINE#         23
  C:1B00H         LINE#         24
  C:1B02H         LINE#         25
  -------         ENDPROC       DS18B20_RESET
  -------         PROC          DS18B20_CHECK
  -------         DO            
  D:0004H         SYMBOL        time_temp
  -------         ENDDO         
  C:18C0H         LINE#         34
  C:18C0H         LINE#         35
  C:18C0H         LINE#         36
  C:18C2H         LINE#         38
  C:18CBH         LINE#         39
  C:18CBH         LINE#         40
  C:18CCH         LINE#         41
  C:18D1H         LINE#         42
  C:18D3H         LINE#         43
  C:18DCH         LINE#         44
  C:18DEH         LINE#         45
  C:18E7H         LINE#         46
  C:18E7H         LINE#         47
  C:18E8H         LINE#         48
  C:18EDH         LINE#         49
  C:18EFH         LINE#         50
  C:18F8H         LINE#         51
  C:18FAH         LINE#         52
  -------         ENDPROC       DS18B20_CHECK
  -------         PROC          DS18B20_READ_BIT
  -------         DO            
  D:0004H         SYMBOL        dat
  -------         ENDDO         
  C:1A57H         LINE#         60
  C:1A57H         LINE#         61
  C:1A57H         LINE#         62
  C:1A59H         LINE#         64
  C:1A5BH         LINE#         65
  C:1A5DH         LINE#         66
  C:1A5FH         LINE#         67
  C:1A61H         LINE#         68
  C:1A67H         LINE#         69
  C:1A69H         LINE#         70
  C:1A6EH         LINE#         71
  C:1A70H         LINE#         72
  -------         ENDPROC       DS18B20_READ_BIT
  -------         PROC          DS18B20_READ_BYTE
  -------         DO            
  D:0003H         SYMBOL        i
  D:0002H         SYMBOL        dat
  D:0007H         SYMBOL        temp
  -------         ENDDO         
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  16:59:15  PAGE 12


  C:1A07H         LINE#         80
  C:1A07H         LINE#         81
  C:1A07H         LINE#         82
  C:1A09H         LINE#         83
  C:1A0AH         LINE#         84
  C:1A0BH         LINE#         86
  C:1A0BH         LINE#         87
  C:1A0BH         LINE#         88
  C:1A0EH         LINE#         89
  C:1A1BH         LINE#         90
  C:1A1FH         LINE#         91
  C:1A21H         LINE#         92
  -------         ENDPROC       DS18B20_READ_BYTE
  -------         PROC          L?0035
  -------         ENDPROC       L?0035
  -------         PROC          _DS18B20_WRITE_BYTE
  D:0004H         SYMBOL        dat
  -------         DO            
  D:0003H         SYMBOL        i
  D:0002H         SYMBOL        temp
  -------         ENDDO         
  C:1938H         LINE#         100
  C:193AH         LINE#         101
  C:193AH         LINE#         102
  C:193CH         LINE#         103
  C:193CH         LINE#         105
  C:193CH         LINE#         106
  C:193CH         LINE#         107
  C:1940H         LINE#         108
  C:1944H         LINE#         109
  C:1947H         LINE#         110
  C:1947H         LINE#         111
  C:1949H         LINE#         112
  C:194BH         LINE#         113
  C:194DH         LINE#         114
  C:1952H         LINE#         115
  C:1954H         LINE#         117
  C:1954H         LINE#         118
  C:1956H         LINE#         119
  C:195BH         LINE#         120
  C:195DH         LINE#         121
  C:195FH         LINE#         122
  C:195FH         LINE#         123
  C:1963H         LINE#         124
  -------         ENDPROC       _DS18B20_WRITE_BYTE
  -------         PROC          DS18B20_START
  C:0003H         LINE#         132
  C:0003H         LINE#         133
  C:0003H         LINE#         134
  C:0003H         LINE#         135
  C:0003H         LINE#         136
  C:0006H         LINE#         137
  -------         ENDPROC       DS18B20_START
  -------         PROC          DS18B20_INIT
  C:1B13H         LINE#         146
  C:1B13H         LINE#         147
  C:1B13H         LINE#         148
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  16:59:15  PAGE 13


  C:1B16H         LINE#         149
  C:1B19H         LINE#         150
  -------         ENDPROC       DS18B20_INIT
  -------         PROC          DS18B20_READ_TEMPERTURE
  -------         DO            
  D:0008H         SYMBOL        temp
  D:0007H         SYMBOL        dath
  D:0001H         SYMBOL        datl
  D:000CH         SYMBOL        value
  -------         ENDDO         
  C:1747H         LINE#         158
  C:1747H         LINE#         159
  C:1747H         LINE#         161
  C:1749H         LINE#         162
  C:174AH         LINE#         163
  C:174EH         LINE#         165
  C:1751H         LINE#         166
  C:1751H         LINE#         167
  C:1751H         LINE#         168
  C:1754H         LINE#         169
  C:1759H         LINE#         171
  C:175EH         LINE#         172
  C:1761H         LINE#         173
  C:176DH         LINE#         175
  C:1774H         LINE#         176
  C:1774H         LINE#         177
  C:1785H         LINE#         178
  C:1793H         LINE#         179
  C:1795H         LINE#         181
  C:1795H         LINE#         182
  C:17AFH         LINE#         183
  C:17AFH         LINE#         184
  C:17B7H         LINE#         185
  -------         ENDPROC       DS18B20_READ_TEMPERTURE
  -------         ENDMOD        DS18B20

  -------         MODULE        LCD9648
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0080H         PUBLIC        P0
  D:0090H         PUBLIC        P1
  D:00A0H         PUBLIC        P2
  D:00B0H         PUBLIC        P3
  C:1A33H         PUBLIC        _WriteData
  C:1535H         PUBLIC        _LCD9648_Write16EnCHAR1
  D:00A8H         PUBLIC        IE
  D:00B8H         PUBLIC        IP
  C:1AD1H         PUBLIC        _WriteComm
  B:00A0H.7       PUBLIC        RS
  C:17B8H         PUBLIC        LCD9648_Init
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  B:0080H.0       PUBLIC        CS0
  C:000EH         PUBLIC        CN16CHARfont
  C:0250H         PUBLIC        EN16CHARfont
  B:00A0H.5       PUBLIC        SDA
  C:1335H         PUBLIC        _LCD9648_Write16CnCHAR
  C:13F8H         PUBLIC        _LCD9648_Write16EnCHAR
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  16:59:15  PAGE 14


  B:00A0H.6       PUBLIC        SCL
  D:00C8H         PUBLIC        T2CON
  C:1A71H         PUBLIC        _SendDataSPI
  B:0080H.1       PUBLIC        RST
  C:1997H         PUBLIC        LCD9648_Clear
  D:00D0H         PUBLIC        PSW
  -------         PROC          _SENDDATASPI
  D:0007H         SYMBOL        dat
  -------         DO            
  D:0006H         SYMBOL        i
  -------         ENDDO         
  C:1A71H         LINE#         4
  C:1A71H         LINE#         5
  C:1A71H         LINE#         8
  C:1A73H         LINE#         9
  C:1A73H         LINE#         10
  C:1A7BH         LINE#         11
  C:1A7DH         LINE#         13
  C:1A81H         LINE#         15
  C:1A83H         LINE#         16
  C:1A85H         LINE#         17
  C:1A89H         LINE#         18
  -------         ENDPROC       _SENDDATASPI
  C:1ACBH         SYMBOL        L?0067
  C:1ACDH         SYMBOL        L?0068
  -------         PROC          L?0066
  -------         ENDPROC       L?0066
  C:1ACBH         SYMBOL        L?0067
  C:1ACDH         SYMBOL        L?0068
  -------         PROC          _WRITECOMM
  D:0006H         SYMBOL        i
  C:1AD1H         LINE#         20
  C:1AD1H         LINE#         21
  C:1AD1H         LINE#         23
  C:1AD3H         LINE#         24
  C:1AD5H         LINE#         26
  C:1AD8H         LINE#         28
  C:1ADAH         LINE#         30
  -------         ENDPROC       _WRITECOMM
  C:1A27H         SYMBOL        L?0070
  -------         PROC          L?0069
  -------         ENDPROC       L?0069
  C:1A27H         SYMBOL        L?0070
  -------         PROC          _WRITEDATA
  D:0006H         SYMBOL        i
  C:1A33H         LINE#         31
  C:1A33H         LINE#         32
  C:1A33H         LINE#         33
  C:1A35H         LINE#         34
  C:1A37H         LINE#         36
  C:1A3AH         LINE#         38
  C:1A3CH         LINE#         39
  -------         ENDPROC       _WRITEDATA
  -------         PROC          LCD9648_INIT
  -------         DO            
  D:0006H         SYMBOL        i
  -------         ENDDO         
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  16:59:15  PAGE 15


  C:17B8H         LINE#         41
  C:17B8H         LINE#         42
  C:17B8H         LINE#         46
  C:17BAH         LINE#         47
  C:17C8H         LINE#         49
  C:17CAH         LINE#         50
  C:17D8H         LINE#         52
  C:17DAH         LINE#         53
  C:17E8H         LINE#         55
  C:17EFH         LINE#         56
  C:17F6H         LINE#         57
  C:17FDH         LINE#         58
  C:1804H         LINE#         59
  C:180BH         LINE#         60
  C:1812H         LINE#         61
  C:1819H         LINE#         62
  -------         ENDPROC       LCD9648_INIT
  -------         PROC          LCD9648_CLEAR
  -------         DO            
  D:0005H         SYMBOL        i
  D:0004H         SYMBOL        j
  -------         ENDDO         
  C:1997H         LINE#         67
  C:1997H         LINE#         68
  C:1997H         LINE#         71
  C:1999H         LINE#         72
  C:1999H         LINE#         73
  C:19A0H         LINE#         74
  C:19A6H         LINE#         75
  C:19ADH         LINE#         76
  C:19B3H         LINE#         78
  C:19B5H         LINE#         79
  C:19B5H         LINE#         80
  C:19BBH         LINE#         81
  C:19BFH         LINE#         82
  C:19C3H         LINE#         83
  -------         ENDPROC       LCD9648_CLEAR
  -------         PROC          _LCD9648_WRITE16CNCHAR
  D:0062H         SYMBOL        x
  D:0063H         SYMBOL        y
  D:0001H         SYMBOL        cn
  -------         DO            
  D:0004H         SYMBOL        j
  D:0064H         SYMBOL        x1
  D:0065H         SYMBOL        x2
  D:0005H         SYMBOL        wordNum
  -------         ENDDO         
  C:1335H         LINE#         86
  C:1339H         LINE#         87
  C:1339H         LINE#         91
  C:1340H         LINE#         92
  C:1340H         LINE#         93
  C:1343H         LINE#         94
  C:1343H         LINE#         97
  C:134AH         LINE#         98
  C:134AH         LINE#         99
  C:134DH         LINE#         100
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  16:59:15  PAGE 16


  C:134DH         LINE#         101
  C:1353H         LINE#         103
  C:1359H         LINE#         104
  C:1361H         LINE#         105
  C:1361H         LINE#         108
  C:1368H         LINE#         110
  C:136FH         LINE#         111
  C:1375H         LINE#         113
  C:1378H         LINE#         114
  C:137FH         LINE#         115
  C:1381H         LINE#         116
  C:1381H         LINE#         118
  C:13B4H         LINE#         120
  C:13B4H         LINE#         121
  C:13B5H         LINE#         122
  C:13B5H         LINE#         123
  C:13BAH         LINE#         124
  C:13BAH         LINE#         126
  C:13C1H         LINE#         129
  C:13C4H         LINE#         130
  C:13CBH         LINE#         131
  C:13CBH         LINE#         132
  C:13DBH         LINE#         133
  C:13DFH         LINE#         134
  C:13E5H         LINE#         135
  C:13E5H         LINE#         136
  C:13EBH         LINE#         137
  C:13F2H         LINE#         139
  C:13F5H         LINE#         140
  C:13F7H         LINE#         141
  -------         ENDPROC       _LCD9648_WRITE16CNCHAR
  -------         PROC          _LCD9648_WRITE16ENCHAR
  D:0075H         SYMBOL        x
  D:0076H         SYMBOL        y
  D:0001H         SYMBOL        en
  -------         DO            
  D:0004H         SYMBOL        j
  D:0077H         SYMBOL        x1
  D:0078H         SYMBOL        x2
  D:0005H         SYMBOL        wordNum
  -------         ENDDO         
  C:13F8H         LINE#         143
  C:13FCH         LINE#         144
  C:13FCH         LINE#         148
  C:1403H         LINE#         149
  C:1403H         LINE#         150
  C:1406H         LINE#         151
  C:1406H         LINE#         154
  C:140DH         LINE#         155
  C:140DH         LINE#         156
  C:1410H         LINE#         157
  C:1410H         LINE#         158
  C:1416H         LINE#         160
  C:141CH         LINE#         161
  C:1424H         LINE#         162
  C:1424H         LINE#         165
  C:142BH         LINE#         167
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  16:59:15  PAGE 17


  C:1432H         LINE#         168
  C:1438H         LINE#         170
  C:143DH         LINE#         171
  C:1444H         LINE#         172
  C:1446H         LINE#         173
  C:1446H         LINE#         175
  C:145EH         LINE#         176
  C:145EH         LINE#         177
  C:145FH         LINE#         178
  C:145FH         LINE#         179
  C:1464H         LINE#         180
  C:1464H         LINE#         182
  C:146BH         LINE#         185
  C:1470H         LINE#         186
  C:1477H         LINE#         187
  C:1477H         LINE#         188
  C:1487H         LINE#         189
  C:148BH         LINE#         190
  C:1491H         LINE#         191
  C:1491H         LINE#         192
  C:1497H         LINE#         193
  C:149EH         LINE#         195
  C:14A1H         LINE#         196
  C:14A3H         LINE#         197
  -------         ENDPROC       _LCD9648_WRITE16ENCHAR
  -------         PROC          _LCD9648_WRITE16ENCHAR1
  D:0001H         SYMBOL        x
  D:0005H         SYMBOL        y
  D:0003H         SYMBOL        en
  -------         DO            
  D:0002H         SYMBOL        j
  D:0014H         SYMBOL        x1
  D:0015H         SYMBOL        x2
  D:0004H         SYMBOL        wordNum
  -------         ENDDO         
  C:1535H         LINE#         198
  C:1537H         LINE#         199
  C:1537H         LINE#         203
  C:153DH         LINE#         204
  C:153DH         LINE#         205
  C:1540H         LINE#         206
  C:1540H         LINE#         209
  C:1546H         LINE#         210
  C:1546H         LINE#         211
  C:1549H         LINE#         212
  C:1549H         LINE#         213
  C:154DH         LINE#         215
  C:1553H         LINE#         217
  C:1553H         LINE#         220
  C:155AH         LINE#         222
  C:1560H         LINE#         223
  C:1565H         LINE#         225
  C:156AH         LINE#         226
  C:1571H         LINE#         227
  C:1573H         LINE#         228
  C:1573H         LINE#         230
  C:1587H         LINE#         231
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  16:59:15  PAGE 18


  C:1587H         LINE#         232
  C:1588H         LINE#         233
  C:1588H         LINE#         234
  C:158DH         LINE#         235
  C:158DH         LINE#         237
  C:1593H         LINE#         240
  C:1598H         LINE#         241
  C:159FH         LINE#         242
  C:159FH         LINE#         243
  C:15B4H         LINE#         244
  C:15B8H         LINE#         245
  C:15BCH         LINE#         246
  C:15BCH         LINE#         247
  C:15C0H         LINE#         248
  C:15C0H         LINE#         250
  C:15C0H         LINE#         251
  C:15C2H         LINE#         252
  -------         ENDPROC       _LCD9648_WRITE16ENCHAR1
  -------         ENDMOD        LCD9648

  -------         MODULE        KEY
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  C:1A8AH         PUBLIC        Key_Read
  D:00A8H         PUBLIC        IE
  D:00B8H         PUBLIC        IP
  B:0090H.0       PUBLIC        k1
  B:0090H.1       PUBLIC        k2
  B:0090H.2       PUBLIC        k3
  B:0090H.3       PUBLIC        k4
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  D:00C8H         PUBLIC        T2CON
  D:00D0H         PUBLIC        PSW
  -------         PROC          KEY_READ
  -------         DO            
  D:0007H         SYMBOL        temp
  -------         ENDDO         
  C:1A8AH         LINE#         4
  C:1A8AH         LINE#         5
  C:1A8AH         LINE#         6
  C:1A8CH         LINE#         8
  C:1A90H         LINE#         9
  C:1A95H         LINE#         10
  C:1A9AH         LINE#         11
  C:1A9FH         LINE#         12
  C:1A9FH         LINE#         13
  -------         ENDPROC       KEY_READ
  -------         ENDMOD        KEY

  -------         MODULE        RTC3085
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  D:00A8H         PUBLIC        IE
  C:18FBH         PUBLIC        _Ds1302Write
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  16:59:15  PAGE 19


  C:1AA0H         PUBLIC        Ds1302ReadTime
  C:19C4H         PUBLIC        Ds1302Init
  D:00B8H         PUBLIC        IP
  B:00B0H.6       PUBLIC        SCLK
  B:00B0H.4       PUBLIC        DSIO
  D:000EH         PUBLIC        TIME
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  C:1B0DH         PUBLIC        WRITE_RTC_ADDR
  C:1B07H         PUBLIC        READ_RTC_ADDR
  D:00C8H         PUBLIC        T2CON
  B:00B0H.5       PUBLIC        RST
  D:00D0H         PUBLIC        PSW
  C:1876H         PUBLIC        _Ds1302Read
  -------         PROC          _DS1302WRITE
  D:0007H         SYMBOL        addr
  D:0005H         SYMBOL        dat
  -------         DO            
  D:0006H         SYMBOL        n
  -------         ENDDO         
  C:18FBH         LINE#         25
  C:18FBH         LINE#         26
  C:18FBH         LINE#         28
  C:18FDH         LINE#         29
  C:18FEH         LINE#         31
  C:1900H         LINE#         32
  C:1901H         LINE#         33
  C:1903H         LINE#         34
  C:1904H         LINE#         36
  C:1906H         LINE#         37
  C:1906H         LINE#         38
  C:190AH         LINE#         39
  C:190EH         LINE#         40
  C:1910H         LINE#         41
  C:1911H         LINE#         42
  C:1913H         LINE#         43
  C:1914H         LINE#         44
  C:1918H         LINE#         45
  C:191AH         LINE#         46
  C:191AH         LINE#         47
  C:191EH         LINE#         48
  C:1922H         LINE#         49
  C:1924H         LINE#         50
  C:1925H         LINE#         51
  C:1927H         LINE#         52
  C:1928H         LINE#         53
  C:192CH         LINE#         55
  C:192EH         LINE#         56
  C:192FH         LINE#         57
  -------         ENDPROC       _DS1302WRITE
  -------         PROC          _DS1302READ
  D:0007H         SYMBOL        addr
  -------         DO            
  D:0005H         SYMBOL        n
  D:0006H         SYMBOL        dat
  D:0007H         SYMBOL        dat1
  -------         ENDDO         
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  16:59:15  PAGE 20


  C:1876H         LINE#         66
  C:1876H         LINE#         67
  C:1876H         LINE#         69
  C:1878H         LINE#         70
  C:1879H         LINE#         72
  C:187BH         LINE#         73
  C:187CH         LINE#         74
  C:187EH         LINE#         75
  C:187FH         LINE#         77
  C:1881H         LINE#         78
  C:1881H         LINE#         79
  C:1885H         LINE#         80
  C:1889H         LINE#         81
  C:188BH         LINE#         82
  C:188CH         LINE#         83
  C:188EH         LINE#         84
  C:188FH         LINE#         85
  C:1893H         LINE#         86
  C:1894H         LINE#         87
  C:1896H         LINE#         88
  C:1896H         LINE#         89
  C:189BH         LINE#         90
  C:18A7H         LINE#         91
  C:18A9H         LINE#         92
  C:18AAH         LINE#         93
  C:18ACH         LINE#         94
  C:18ADH         LINE#         95
  C:18B1H         LINE#         97
  C:18B3H         LINE#         98
  C:18B4H         LINE#         99
  C:18B6H         LINE#         100
  C:18B7H         LINE#         101
  C:18B9H         LINE#         102
  C:18BAH         LINE#         103
  C:18BCH         LINE#         104
  C:18BDH         LINE#         105
  C:18BFH         LINE#         106
  -------         ENDPROC       _DS1302READ
  -------         PROC          DS1302INIT
  -------         DO            
  D:0004H         SYMBOL        n
  -------         ENDDO         
  C:19C4H         LINE#         115
  C:19C4H         LINE#         116
  C:19C4H         LINE#         118
  C:19CBH         LINE#         119
  C:19CDH         LINE#         120
  C:19CDH         LINE#         121
  C:19DCH         LINE#         122
  C:19E0H         LINE#         123
  -------         ENDPROC       DS1302INIT
  -------         PROC          DS1302READTIME
  -------         DO            
  D:0003H         SYMBOL        n
  -------         ENDDO         
  C:1AA0H         LINE#         133
  C:1AA0H         LINE#         134
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  16:59:15  PAGE 21


  C:1AA0H         LINE#         136
  C:1AA2H         LINE#         137
  C:1AA2H         LINE#         138
  C:1AB1H         LINE#         139
  C:1AB5H         LINE#         141
  -------         ENDPROC       DS1302READTIME
  -------         ENDMOD        RTC3085

  -------         MODULE        ?C?FPADD
  C:08A6H         PUBLIC        ?C?FPADD
  C:08A2H         PUBLIC        ?C?FPSUB
  -------         ENDMOD        ?C?FPADD

  -------         MODULE        ?C?FPMUL
  C:0997H         PUBLIC        ?C?FPMUL
  -------         ENDMOD        ?C?FPMUL

  -------         MODULE        ?C?FPCMP
  C:0A9FH         PUBLIC        ?C?FPCMP
  C:0A9DH         PUBLIC        ?C?FPCMP3
  -------         ENDMOD        ?C?FPCMP

  -------         MODULE        ?C?FCAST
  C:0B20H         PUBLIC        ?C?FCASTC
  C:0B1BH         PUBLIC        ?C?FCASTI
  C:0B16H         PUBLIC        ?C?FCASTL
  -------         ENDMOD        ?C?FCAST

  -------         MODULE        ?C?CASTF
  C:0B54H         PUBLIC        ?C?CASTF
  -------         ENDMOD        ?C?CASTF

  -------         MODULE        ?C?CLDPTR
  C:0BD5H         PUBLIC        ?C?CLDPTR
  -------         ENDMOD        ?C?CLDPTR

  -------         MODULE        ?C?CLDOPTR
  C:0BEEH         PUBLIC        ?C?CLDOPTR
  -------         ENDMOD        ?C?CLDOPTR

  -------         MODULE        ?C?CSTPTR
  C:0C1BH         PUBLIC        ?C?CSTPTR
  -------         ENDMOD        ?C?CSTPTR

  -------         MODULE        ?C?CSTOPTR
  C:0C2DH         PUBLIC        ?C?CSTOPTR
  -------         ENDMOD        ?C?CSTOPTR

  -------         MODULE        ?C?LNEG
  C:0C4FH         PUBLIC        ?C?LNEG
  -------         ENDMOD        ?C?LNEG

  -------         MODULE        ?C?LLDIDATA
  C:0C5DH         PUBLIC        ?C?LLDIDATA
  -------         ENDMOD        ?C?LLDIDATA

  -------         MODULE        ?C?LSTIDATA
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  16:59:15  PAGE 22


  C:0C69H         PUBLIC        ?C?LSTIDATA
  -------         ENDMOD        ?C?LSTIDATA

  -------         MODULE        ?C?LSTKIDATA
  C:0C75H         PUBLIC        ?C?LSTKIDATA
  -------         ENDMOD        ?C?LSTKIDATA

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?_BCD_TO_DEC?MAIN

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?DS18B20_INIT?DS18B20

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?DS18B20_READ_TEMPERTURE?DS18B20

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?_LCD9648_WRITE16ENCHAR1?LCD9648

Program Size: data=116.1 xdata=0 code=6938
LINK/LOCATE RUN COMPLETE.  4 WARNING(S),  0 ERROR(S)
