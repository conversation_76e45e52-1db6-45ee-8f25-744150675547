BL<PERSON> BANKED LINKER/LOCATER V6.22                                                        07/08/2025  10:52:21  PAGE 1


BL51 BANKED LINKER/LOCATER V6.22, INVOKED BY:
E:\KEIL\C51\BIN\BL51.EXE .\Objects\main.obj, .\Objects\ds18b20.obj, .\Objects\lcd9648.obj, .\Objects\Key.obj, .\Objects\
>> rtc3085.obj TO .\Objects\Project PRINT (.\Listings\Project.m51) RAMSIZE (256)


MEMORY MODEL: SMALL WITH FLOATING POINT ARITHMETIC


INPUT MODULES INCLUDED:
  .\Objects\main.obj (MAIN)
  .\Objects\ds18b20.obj (DS18B20)
  .\Objects\lcd9648.obj (LCD9648)
  .\Objects\Key.obj (KEY)
  .\Objects\rtc3085.obj (RTC3085)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?FPADD)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?FPMUL)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?FPCMP)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?FCAST)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?CASTF)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?FPGETOPN)
  E:\KEIL\C51\LIB\C51S.LIB (?C_STARTUP)
  E:\KEIL\C51\LIB\C51S.LIB (?C?CLDPTR)
  E:\KEIL\C51\LIB\C51S.LIB (?C?CLDOPTR)
  E:\KEIL\C51\LIB\C51S.LIB (?C?CSTPTR)
  E:\KEIL\C51\LIB\C51S.LIB (?C?CSTOPTR)
  E:\KEIL\C51\LIB\C51S.LIB (?C?LNEG)
  E:\KEIL\C51\LIB\C51S.LIB (?C?LLDIDATA)
  E:\KEIL\C51\LIB\C51S.LIB (?C?LSTIDATA)
  E:\KEIL\C51\LIB\C51S.LIB (?C?LSTKIDATA)
  E:\KEIL\C51\LIB\C51S.LIB (?C_INIT)


LINK MAP OF MODULE:  .\Objects\Project (MAIN)


            TYPE    BASE      LENGTH    RELOCATION   SEGMENT NAME
            -----------------------------------------------------

            * * * * * * *   D A T A   M E M O R Y   * * * * * * *
            REG     0000H     0008H     ABSOLUTE     "REG BANK 0"
            DATA    0008H     0006H     UNIT         ?DT?DS18B20_READ_TEMPERTURE?DS18B20
            DATA    000EH     0006H     UNIT         ?DT?RTC3085
            DATA    0014H     0002H     UNIT         ?DT?_LCD9648_WRITE16ENCHAR1?LCD9648
                    0016H     000AH                  *** GAP ***
            BIT     0020H.0   0000H.1   UNIT         ?BI?MAIN
                    0020H.1   0000H.7                *** GAP ***
            DATA    0021H     0041H     UNIT         ?DT?MAIN
            DATA    0062H     001CH     UNIT         _DATA_GROUP_
            IDATA   007EH     0001H     UNIT         ?STACK

            * * * * * * *   C O D E   M E M O R Y   * * * * * * *
            CODE    0000H     0003H     ABSOLUTE     
            CODE    0003H     0008H     UNIT         ?PR?DS18B20_START?DS18B20
            CODE    000BH     0003H     ABSOLUTE     
            CODE    000EH     0891H     UNIT         ?CO?LCD9648
            CODE    089FH     03EFH     UNIT         ?C?LIB_CODE
            CODE    0C8EH     02C4H     UNIT         ?PR?KEY_PROC?MAIN
            CODE    0F52H     027AH     UNIT         ?PR?LCD_PROC?MAIN
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  10:52:21  PAGE 2


            CODE    11CCH     016CH     UNIT         ?PR?LCD9648_INIT_PROC?MAIN
            CODE    1338H     00C3H     UNIT         ?PR?_LCD9648_WRITE16CNCHAR?LCD9648
            CODE    13FBH     00B3H     UNIT         ?PR?_TIME_TO_STRING?MAIN
            CODE    14AEH     00ACH     UNIT         ?PR?_LCD9648_WRITE16ENCHAR?LCD9648
            CODE    155AH     0095H     UNIT         ?CO?MAIN
            CODE    15EFH     0091H     UNIT         ?PR?_FLOAT_TO_STRING?MAIN
            CODE    1680H     008EH     UNIT         ?PR?_LCD9648_WRITE16ENCHAR1?LCD9648
            CODE    170EH     008CH     UNIT         ?C_C51STARTUP
            CODE    179AH     0071H     UNIT         ?PR?DS18B20_READ_TEMPERTURE?DS18B20
            CODE    180BH     0068H     UNIT         ?PR?LCD9648_INIT?LCD9648
            CODE    1873H     0056H     UNIT         ?C_INITSEG
            CODE    18C9H     004AH     UNIT         ?PR?_DS1302READ?RTC3085
            CODE    1913H     003BH     UNIT         ?PR?DS18B20_CHECK?DS18B20
            CODE    194EH     0035H     UNIT         ?PR?_DS1302WRITE?RTC3085
            CODE    1983H     0034H     UNIT         ?PR?_DS18B20_WRITE_BYTE?DS18B20
            CODE    19B7H     0033H     UNIT         ?PR?TIMER0_ISR?MAIN
            CODE    19EAH     002DH     UNIT         ?PR?LCD9648_CLEAR?LCD9648
            CODE    1A17H     0023H     UNIT         ?PR?DS1302INIT?RTC3085
            CODE    1A3AH     0020H     UNIT         ?PR?LCD_INIT_TEST?MAIN
            CODE    1A5AH     001BH     UNIT         ?PR?DS18B20_READ_BYTE?DS18B20
            CODE    1A75H     001BH     UNIT         ?PR?_WRITEDATA?LCD9648
            CODE    1A90H     001AH     UNIT         ?PR?MAIN?MAIN
            CODE    1AAAH     001AH     UNIT         ?PR?DS18B20_READ_BIT?DS18B20
            CODE    1AC4H     0019H     UNIT         ?PR?_SENDDATASPI?LCD9648
            CODE    1ADDH     0016H     UNIT         ?PR?KEY_READ?KEY
            CODE    1AF3H     0016H     UNIT         ?PR?DS1302READTIME?RTC3085
            CODE    1B09H     0013H     UNIT         ?PR?TIMER0_INIT?MAIN
            CODE    1B1CH     0012H     UNIT         ?PR?_WRITECOMM?LCD9648
            CODE    1B2EH     000FH     UNIT         ?PR?_BCD_TO_DEC?MAIN
            CODE    1B3DH     000FH     UNIT         ?PR?_DELAY_10US?DS18B20
            CODE    1B4CH     000EH     UNIT         ?PR?DS18B20_RESET?DS18B20
            CODE    1B5AH     000CH     UNIT         ?CO?RTC3085
            CODE    1B66H     0007H     UNIT         ?PR?DS18B20_INIT?DS18B20



OVERLAY MAP OF MODULE:   .\Objects\Project (MAIN)


SEGMENT                                       DATA_GROUP 
  +--> CALLED SEGMENT                       START    LENGTH
-----------------------------------------------------------
?C_C51STARTUP                               -----    -----
  +--> ?PR?MAIN?MAIN
  +--> ?C_INITSEG

?PR?MAIN?MAIN                               -----    -----
  +--> ?PR?DS1302INIT?RTC3085
  +--> ?PR?LCD9648_INIT_PROC?MAIN
  +--> ?PR?TIMER0_INIT?MAIN
  +--> ?PR?LCD_INIT_TEST?MAIN
  +--> ?PR?KEY_PROC?MAIN
  +--> ?PR?LCD_PROC?MAIN

?PR?DS1302INIT?RTC3085                      -----    -----
  +--> ?PR?_DS1302WRITE?RTC3085
  +--> ?CO?RTC3085
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  10:52:21  PAGE 3



?PR?LCD9648_INIT_PROC?MAIN                  -----    -----
  +--> ?PR?LCD9648_INIT?LCD9648
  +--> ?PR?LCD9648_CLEAR?LCD9648
  +--> ?CO?MAIN
  +--> ?PR?_LCD9648_WRITE16CNCHAR?LCD9648
  +--> ?PR?_LCD9648_WRITE16ENCHAR?LCD9648

?PR?LCD9648_INIT?LCD9648                    -----    -----
  +--> ?PR?_WRITECOMM?LCD9648

?PR?_WRITECOMM?LCD9648                      -----    -----
  +--> ?PR?_SENDDATASPI?LCD9648

?PR?LCD9648_CLEAR?LCD9648                   -----    -----
  +--> ?PR?_WRITECOMM?LCD9648
  +--> ?PR?_WRITEDATA?LCD9648

?PR?_WRITEDATA?LCD9648                      -----    -----
  +--> ?PR?_SENDDATASPI?LCD9648

?PR?_LCD9648_WRITE16CNCHAR?LCD9648          0062H    0004H
  +--> ?PR?_WRITECOMM?LCD9648
  +--> ?CO?LCD9648
  +--> ?PR?_WRITEDATA?LCD9648

?PR?_LCD9648_WRITE16ENCHAR?LCD9648          0075H    0004H
  +--> ?PR?_WRITECOMM?LCD9648
  +--> ?CO?LCD9648
  +--> ?PR?_WRITEDATA?LCD9648

?PR?LCD_INIT_TEST?MAIN                      -----    -----
  +--> ?PR?LCD9648_INIT_PROC?MAIN

?PR?KEY_PROC?MAIN                           -----    -----
  +--> ?PR?KEY_READ?KEY

?PR?LCD_PROC?MAIN                           0062H    0013H
  +--> ?PR?LCD9648_CLEAR?LCD9648
  +--> ?CO?MAIN
  +--> ?PR?_LCD9648_WRITE16ENCHAR?LCD9648
  +--> ?PR?_FLOAT_TO_STRING?MAIN
  +--> ?PR?_TIME_TO_STRING?MAIN

?PR?_FLOAT_TO_STRING?MAIN                   0075H    0009H

?PR?_TIME_TO_STRING?MAIN                    0075H    0003H
  +--> ?PR?DS1302READTIME?RTC3085
  +--> ?PR?_BCD_TO_DEC?MAIN

?PR?DS1302READTIME?RTC3085                  -----    -----
  +--> ?CO?RTC3085
  +--> ?PR?_DS1302READ?RTC3085



SYMBOL TABLE OF MODULE:  .\Objects\Project (MAIN)
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  10:52:21  PAGE 4



  VALUE           TYPE          NAME
  ----------------------------------

  -------         MODULE        MAIN
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0080H         PUBLIC        P0
  D:0090H         PUBLIC        P1
  D:00A0H         PUBLIC        P2
  D:00B0H         PUBLIC        P3
  C:19B7H         PUBLIC        Timer0_ISR
  B:00A8H.7       PUBLIC        EA
  D:00A8H         PUBLIC        IE
  C:13FBH         PUBLIC        _Time_To_String
  D:0021H         PUBLIC        Last_LCD_Page_Mode
  D:0022H         PUBLIC        Temperature
  D:0023H         PUBLIC        Display_Buffer
  D:00B8H         PUBLIC        IP
  D:0033H         PUBLIC        LCD_Page_Mode
  C:11CCH         PUBLIC        LCD9648_Init_Proc
  B:0020H.0       PUBLIC        LCD_Init_End_Flag
  C:0C8EH         PUBLIC        Key_Proc
  D:0034H         PUBLIC        Key_Down
  C:1A90H         PUBLIC        main
  C:1B09H         PUBLIC        Timer0_Init
  D:0035H         PUBLIC        Key_Old
  D:0036H         PUBLIC        Key_Slow_Down
  D:0037H         PUBLIC        Key_Val
  D:0038H         PUBLIC        Last_Display_Page
  D:0039H         PUBLIC        Last_LCD_Mode
  D:003AH         PUBLIC        LCD_Init_Mode
  D:0098H         PUBLIC        SCON
  D:0089H         PUBLIC        TMOD
  D:0088H         PUBLIC        TCON
  D:003BH         PUBLIC        Last_Setting_Page
  D:003CH         PUBLIC        Display_Page
  D:003DH         PUBLIC        Setting_Index
  D:003EH         PUBLIC        Timer0_count
  D:0040H         PUBLIC        Setting_Page
  B:00A8H.1       PUBLIC        ET0
  C:0F52H         PUBLIC        LCD_Proc
  D:008CH         PUBLIC        TH0
  C:1A3AH         PUBLIC        LCD_Init_Test
  D:0041H         PUBLIC        Key_Up
  D:008AH         PUBLIC        TL0
  D:0042H         PUBLIC        Setting_Disp
  D:004EH         PUBLIC        Last_State_Index
  B:0088H.4       PUBLIC        TR0
  D:004FH         PUBLIC        Time_Flag_Count
  C:1B2EH         PUBLIC        _BCD_To_Dec
  D:0050H         PUBLIC        State_Index
  D:00C8H         PUBLIC        T2CON
  D:0051H         PUBLIC        Time_Flag
  D:0052H         PUBLIC        State_Disp
  C:1600H         PUBLIC        _Float_To_String
  D:00D0H         PUBLIC        PSW
  -------         PROC          _BCD_TO_DEC
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  10:52:21  PAGE 5


  D:0007H         SYMBOL        bcd
  C:1B2EH         LINE#         35
  -------         ENDPROC       _BCD_TO_DEC
  -------         PROC          _TIME_TO_STRING
  D:0075H         SYMBOL        time_str
  C:13FBH         LINE#         42
  C:1401H         LINE#         43
  C:1401H         LINE#         44
  C:1404H         LINE#         45
  C:1419H         LINE#         46
  C:1433H         LINE#         47
  C:143BH         LINE#         48
  C:1453H         LINE#         49
  C:146DH         LINE#         50
  C:1475H         LINE#         51
  C:148DH         LINE#         52
  C:14A7H         LINE#         53
  -------         ENDPROC       _TIME_TO_STRING
  -------         PROC          KEY_PROC
  C:0C8EH         LINE#         57
  C:0C8EH         LINE#         58
  C:0C8EH         LINE#         59
  C:0C95H         LINE#         60
  C:0C98H         LINE#         62
  C:0C9DH         LINE#         63
  C:0CA6H         LINE#         64
  C:0CACH         LINE#         65
  C:0CAFH         LINE#         67
  C:0CCBH         LINE#         68
  C:0CCBH         LINE#         69
  C:0CCBH         LINE#         70
  C:0CD4H         LINE#         71
  C:0CD4H         LINE#         72
  C:0CDFH         LINE#         73
  C:0CDFH         LINE#         74
  C:0CFCH         LINE#         75
  C:0D28H         LINE#         76
  C:0D5CH         LINE#         77
  C:0D5DH         LINE#         78
  C:0D66H         LINE#         79
  C:0D66H         LINE#         80
  C:0D84H         LINE#         81
  C:0DAFH         LINE#         82
  C:0DAFH         LINE#         83
  C:0DB0H         LINE#         84
  C:0DB9H         LINE#         85
  C:0DB9H         LINE#         86
  C:0DD7H         LINE#         87
  C:0E03H         LINE#         88
  C:0E03H         LINE#         89
  C:0E04H         LINE#         91
  C:0E04H         LINE#         92
  C:0E0DH         LINE#         93
  C:0E0DH         LINE#         94
  C:0E18H         LINE#         95
  C:0E18H         LINE#         96
  C:0E35H         LINE#         97
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  10:52:21  PAGE 6


  C:0E63H         LINE#         98
  C:0E99H         LINE#         99
  C:0E9AH         LINE#         100
  C:0EA3H         LINE#         101
  C:0EA3H         LINE#         102
  C:0EC1H         LINE#         103
  C:0EE1H         LINE#         104
  C:0EE1H         LINE#         105
  C:0EE3H         LINE#         106
  C:0EE9H         LINE#         107
  C:0EE9H         LINE#         108
  C:0F07H         LINE#         109
  C:0F2FH         LINE#         110
  C:0F2FH         LINE#         111
  C:0F30H         LINE#         113
  C:0F30H         LINE#         114
  C:0F3AH         LINE#         115
  C:0F3AH         LINE#         116
  C:0F3CH         LINE#         118
  C:0F3CH         LINE#         119
  C:0F47H         LINE#         120
  C:0F47H         LINE#         121
  C:0F51H         LINE#         122
  C:0F51H         LINE#         123
  C:0F51H         LINE#         124
  C:0F51H         LINE#         125
  -------         ENDPROC       KEY_PROC
  C:15F7H         SYMBOL        L?0090
  -------         PROC          L?0089
  -------         ENDPROC       L?0089
  C:15F7H         SYMBOL        L?0090
  -------         PROC          _FLOAT_TO_STRING
  D:0075H         SYMBOL        value
  D:0079H         SYMBOL        str
  D:007CH         SYMBOL        unit
  -------         DO            
  D:007DH         SYMBOL        integer_part
  D:0007H         SYMBOL        decimal_part
  -------         ENDDO         
  C:1600H         LINE#         129
  C:160EH         LINE#         130
  C:160EH         LINE#         131
  C:1613H         LINE#         132
  C:1639H         LINE#         134
  C:164AH         LINE#         135
  C:1660H         LINE#         136
  C:1668H         LINE#         137
  C:1671H         LINE#         138
  C:1679H         LINE#         139
  -------         ENDPROC       _FLOAT_TO_STRING
  -------         PROC          TIMER0_INIT
  C:1B09H         LINE#         146
  C:1B09H         LINE#         147
  C:1B09H         LINE#         148
  C:1B0CH         LINE#         149
  C:1B0FH         LINE#         150
  C:1B12H         LINE#         151
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  10:52:21  PAGE 7


  C:1B15H         LINE#         152
  C:1B17H         LINE#         153
  C:1B19H         LINE#         154
  C:1B1BH         LINE#         155
  -------         ENDPROC       TIMER0_INIT
  -------         PROC          TIMER0_ISR
  C:19B7H         LINE#         161
  C:19BBH         LINE#         163
  C:19BEH         LINE#         164
  C:19C1H         LINE#         166
  C:19C9H         LINE#         168
  C:19D2H         LINE#         169
  C:19D2H         LINE#         170
  C:19D5H         LINE#         171
  C:19DBH         LINE#         173
  C:19DBH         LINE#         175
  C:19E5H         LINE#         176
  -------         ENDPROC       TIMER0_ISR
  -------         PROC          LCD9648_INIT_PROC
  C:11CCH         LINE#         177
  C:11CCH         LINE#         178
  C:11CCH         LINE#         179
  C:11E2H         LINE#         180
  C:11E2H         LINE#         181
  C:11E2H         LINE#         182
  C:11E5H         LINE#         183
  C:11E8H         LINE#         185
  C:11F4H         LINE#         186
  C:1201H         LINE#         187
  C:120EH         LINE#         189
  C:121BH         LINE#         191
  C:1228H         LINE#         192
  C:1235H         LINE#         193
  C:1242H         LINE#         194
  C:124FH         LINE#         195
  C:125CH         LINE#         196
  C:125CH         LINE#         198
  C:125CH         LINE#         199
  C:125FH         LINE#         200
  C:1262H         LINE#         202
  C:126EH         LINE#         203
  C:127BH         LINE#         204
  C:1288H         LINE#         205
  C:1295H         LINE#         206
  C:12A2H         LINE#         207
  C:12AFH         LINE#         209
  C:12BCH         LINE#         210
  C:12C9H         LINE#         211
  C:12D6H         LINE#         212
  C:12E3H         LINE#         213
  C:12F0H         LINE#         214
  C:12FDH         LINE#         216
  C:130AH         LINE#         217
  C:1317H         LINE#         218
  C:1324H         LINE#         219
  C:1331H         LINE#         220
  C:1331H         LINE#         222
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  10:52:21  PAGE 8


  C:1331H         LINE#         223
  C:1334H         LINE#         224
  C:1337H         LINE#         232
  C:1337H         LINE#         234
  C:1337H         LINE#         236
  C:1337H         LINE#         237
  C:1337H         LINE#         238
  -------         ENDPROC       LCD9648_INIT_PROC
  -------         PROC          LCD_INIT_TEST
  C:1A3AH         LINE#         240
  C:1A3AH         LINE#         241
  C:1A3AH         LINE#         242
  C:1A3EH         LINE#         243
  C:1A3EH         LINE#         244
  C:1A41H         LINE#         245
  C:1A43H         LINE#         246
  C:1A45H         LINE#         247
  C:1A4CH         LINE#         248
  C:1A4CH         LINE#         249
  C:1A4FH         LINE#         250
  C:1A4FH         LINE#         251
  C:1A54H         LINE#         252
  C:1A59H         LINE#         253
  C:1A59H         LINE#         255
  -------         ENDPROC       LCD_INIT_TEST
  -------         PROC          LCD_PROC
  -------         DO            
  D:0062H         SYMBOL        temp_str
  D:0072H         SYMBOL        i
  D:0073H         SYMBOL        need_clear
  D:0074H         SYMBOL        need_update_pointer
  -------         ENDDO         
  C:0F52H         LINE#         257
  C:0F52H         LINE#         258
  C:0F52H         LINE#         261
  C:0F55H         LINE#         262
  C:0F57H         LINE#         264
  C:0F5DH         LINE#         267
  C:0F63H         LINE#         268
  C:0F63H         LINE#         269
  C:0F66H         LINE#         270
  C:0F69H         LINE#         271
  C:0F6CH         LINE#         272
  C:0F6CH         LINE#         275
  C:0F72H         LINE#         276
  C:0F72H         LINE#         277
  C:0F75H         LINE#         278
  C:0F78H         LINE#         279
  C:0F78H         LINE#         281
  C:0F93H         LINE#         282
  C:0F93H         LINE#         283
  C:0F93H         LINE#         284
  C:0F97H         LINE#         285
  C:0F97H         LINE#         286
  C:0FA4H         LINE#         287
  C:0FB0H         LINE#         288
  C:0FBDH         LINE#         289
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  10:52:21  PAGE 9


  C:0FCAH         LINE#         290
  C:0FCAH         LINE#         293
  C:0FCDH         LINE#         294
  C:0FDAH         LINE#         296
  C:0FE5H         LINE#         297
  C:0FF2H         LINE#         299
  C:1006H         LINE#         300
  C:1010H         LINE#         301
  C:1013H         LINE#         303
  C:1013H         LINE#         304
  C:1017H         LINE#         305
  C:1017H         LINE#         306
  C:1024H         LINE#         307
  C:1031H         LINE#         308
  C:1031H         LINE#         311
  C:103AH         LINE#         312
  C:1044H         LINE#         313
  C:1047H         LINE#         315
  C:1047H         LINE#         316
  C:104BH         LINE#         317
  C:104BH         LINE#         318
  C:1058H         LINE#         319
  C:1065H         LINE#         320
  C:1072H         LINE#         321
  C:107FH         LINE#         322
  C:107FH         LINE#         325
  C:1087H         LINE#         326
  C:1087H         LINE#         327
  C:108AH         LINE#         328
  C:108AH         LINE#         329
  C:108FH         LINE#         330
  C:109AH         LINE#         332
  C:10AAH         LINE#         333
  C:10B3H         LINE#         334
  C:10B3H         LINE#         337
  C:10B6H         LINE#         338
  C:10C3H         LINE#         340
  C:10CEH         LINE#         341
  C:10DBH         LINE#         343
  C:10EFH         LINE#         344
  C:10F9H         LINE#         345
  C:10FCH         LINE#         347
  C:10FCH         LINE#         348
  C:1100H         LINE#         349
  C:1100H         LINE#         350
  C:110DH         LINE#         351
  C:111AH         LINE#         352
  C:1127H         LINE#         353
  C:1134H         LINE#         354
  C:1134H         LINE#         357
  C:113CH         LINE#         358
  C:113CH         LINE#         359
  C:113FH         LINE#         360
  C:113FH         LINE#         361
  C:1144H         LINE#         362
  C:114FH         LINE#         364
  C:115FH         LINE#         365
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  10:52:21  PAGE 10


  C:1168H         LINE#         366
  C:1168H         LINE#         369
  C:117CH         LINE#         370
  C:1189H         LINE#         372
  C:119DH         LINE#         373
  C:11AAH         LINE#         375
  C:11BEH         LINE#         376
  C:11CBH         LINE#         377
  C:11CBH         LINE#         378
  C:11CBH         LINE#         379
  -------         ENDPROC       LCD_PROC
  -------         PROC          MAIN
  C:1A90H         LINE#         381
  C:1A90H         LINE#         382
  C:1A90H         LINE#         384
  C:1A93H         LINE#         385
  C:1A96H         LINE#         388
  C:1A99H         LINE#         390
  C:1A9CH         LINE#         391
  C:1A9FH         LINE#         393
  C:1A9FH         LINE#         394
  C:1A9FH         LINE#         395
  C:1AA2H         LINE#         396
  C:1AA5H         LINE#         397
  C:1AA8H         LINE#         398
  -------         ENDPROC       MAIN
  -------         ENDMOD        MAIN

  -------         MODULE        DS18B20
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  C:1B4CH         PUBLIC        ds18b20_reset
  D:00A8H         PUBLIC        IE
  C:0003H         PUBLIC        ds18b20_start
  D:00B8H         PUBLIC        IP
  C:1A5AH         PUBLIC        ds18b20_read_byte
  C:1B66H         PUBLIC        ds18b20_init
  C:1AAAH         PUBLIC        ds18b20_read_bit
  C:198BH         PUBLIC        _ds18b20_write_byte
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  C:179AH         PUBLIC        ds18b20_read_temperture
  C:1B3DH         PUBLIC        _delay_10us
  B:00B0H.7       PUBLIC        DS18B20_PORT
  D:00C8H         PUBLIC        T2CON
  C:1913H         PUBLIC        ds18b20_check
  D:00D0H         PUBLIC        PSW
  -------         PROC          _DELAY_10US
  D:0007H         SYMBOL        xms
  -------         DO            
  D:0006H         SYMBOL        i
  -------         ENDDO         
  C:1B3DH         LINE#         4
  C:1B3DH         LINE#         5
  C:1B3DH         LINE#         7
  C:1B43H         LINE#         8
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  10:52:21  PAGE 11


  C:1B43H         LINE#         9
  C:1B45H         LINE#         10
  C:1B4BH         LINE#         12
  -------         ENDPROC       _DELAY_10US
  -------         PROC          DS18B20_RESET
  C:1B4CH         LINE#         20
  C:1B4CH         LINE#         21
  C:1B4CH         LINE#         22
  C:1B4EH         LINE#         23
  C:1B53H         LINE#         24
  C:1B55H         LINE#         25
  -------         ENDPROC       DS18B20_RESET
  -------         PROC          DS18B20_CHECK
  -------         DO            
  D:0004H         SYMBOL        time_temp
  -------         ENDDO         
  C:1913H         LINE#         34
  C:1913H         LINE#         35
  C:1913H         LINE#         36
  C:1915H         LINE#         38
  C:191EH         LINE#         39
  C:191EH         LINE#         40
  C:191FH         LINE#         41
  C:1924H         LINE#         42
  C:1926H         LINE#         43
  C:192FH         LINE#         44
  C:1931H         LINE#         45
  C:193AH         LINE#         46
  C:193AH         LINE#         47
  C:193BH         LINE#         48
  C:1940H         LINE#         49
  C:1942H         LINE#         50
  C:194BH         LINE#         51
  C:194DH         LINE#         52
  -------         ENDPROC       DS18B20_CHECK
  -------         PROC          DS18B20_READ_BIT
  -------         DO            
  D:0004H         SYMBOL        dat
  -------         ENDDO         
  C:1AAAH         LINE#         60
  C:1AAAH         LINE#         61
  C:1AAAH         LINE#         62
  C:1AACH         LINE#         64
  C:1AAEH         LINE#         65
  C:1AB0H         LINE#         66
  C:1AB2H         LINE#         67
  C:1AB4H         LINE#         68
  C:1ABAH         LINE#         69
  C:1ABCH         LINE#         70
  C:1AC1H         LINE#         71
  C:1AC3H         LINE#         72
  -------         ENDPROC       DS18B20_READ_BIT
  -------         PROC          DS18B20_READ_BYTE
  -------         DO            
  D:0003H         SYMBOL        i
  D:0002H         SYMBOL        dat
  D:0007H         SYMBOL        temp
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  10:52:21  PAGE 12


  -------         ENDDO         
  C:1A5AH         LINE#         80
  C:1A5AH         LINE#         81
  C:1A5AH         LINE#         82
  C:1A5CH         LINE#         83
  C:1A5DH         LINE#         84
  C:1A5EH         LINE#         86
  C:1A5EH         LINE#         87
  C:1A5EH         LINE#         88
  C:1A61H         LINE#         89
  C:1A6EH         LINE#         90
  C:1A72H         LINE#         91
  C:1A74H         LINE#         92
  -------         ENDPROC       DS18B20_READ_BYTE
  -------         PROC          L?0035
  -------         ENDPROC       L?0035
  -------         PROC          _DS18B20_WRITE_BYTE
  D:0004H         SYMBOL        dat
  -------         DO            
  D:0003H         SYMBOL        i
  D:0002H         SYMBOL        temp
  -------         ENDDO         
  C:198BH         LINE#         100
  C:198DH         LINE#         101
  C:198DH         LINE#         102
  C:198FH         LINE#         103
  C:198FH         LINE#         105
  C:198FH         LINE#         106
  C:198FH         LINE#         107
  C:1993H         LINE#         108
  C:1997H         LINE#         109
  C:199AH         LINE#         110
  C:199AH         LINE#         111
  C:199CH         LINE#         112
  C:199EH         LINE#         113
  C:19A0H         LINE#         114
  C:19A5H         LINE#         115
  C:19A7H         LINE#         117
  C:19A7H         LINE#         118
  C:19A9H         LINE#         119
  C:19AEH         LINE#         120
  C:19B0H         LINE#         121
  C:19B2H         LINE#         122
  C:19B2H         LINE#         123
  C:19B6H         LINE#         124
  -------         ENDPROC       _DS18B20_WRITE_BYTE
  -------         PROC          DS18B20_START
  C:0003H         LINE#         132
  C:0003H         LINE#         133
  C:0003H         LINE#         134
  C:0003H         LINE#         135
  C:0003H         LINE#         136
  C:0006H         LINE#         137
  -------         ENDPROC       DS18B20_START
  -------         PROC          DS18B20_INIT
  C:1B66H         LINE#         146
  C:1B66H         LINE#         147
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  10:52:21  PAGE 13


  C:1B66H         LINE#         148
  C:1B69H         LINE#         149
  C:1B6CH         LINE#         150
  -------         ENDPROC       DS18B20_INIT
  -------         PROC          DS18B20_READ_TEMPERTURE
  -------         DO            
  D:0008H         SYMBOL        temp
  D:0007H         SYMBOL        dath
  D:0001H         SYMBOL        datl
  D:000CH         SYMBOL        value
  -------         ENDDO         
  C:179AH         LINE#         158
  C:179AH         LINE#         159
  C:179AH         LINE#         161
  C:179CH         LINE#         162
  C:179DH         LINE#         163
  C:17A1H         LINE#         165
  C:17A4H         LINE#         166
  C:17A4H         LINE#         167
  C:17A4H         LINE#         168
  C:17A7H         LINE#         169
  C:17ACH         LINE#         171
  C:17B1H         LINE#         172
  C:17B4H         LINE#         173
  C:17C0H         LINE#         175
  C:17C7H         LINE#         176
  C:17C7H         LINE#         177
  C:17D8H         LINE#         178
  C:17E6H         LINE#         179
  C:17E8H         LINE#         181
  C:17E8H         LINE#         182
  C:1802H         LINE#         183
  C:1802H         LINE#         184
  C:180AH         LINE#         185
  -------         ENDPROC       DS18B20_READ_TEMPERTURE
  -------         ENDMOD        DS18B20

  -------         MODULE        LCD9648
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0080H         PUBLIC        P0
  D:0090H         PUBLIC        P1
  D:00A0H         PUBLIC        P2
  D:00B0H         PUBLIC        P3
  C:1A86H         PUBLIC        _WriteData
  C:1680H         PUBLIC        _LCD9648_Write16EnCHAR1
  D:00A8H         PUBLIC        IE
  D:00B8H         PUBLIC        IP
  C:1B24H         PUBLIC        _WriteComm
  B:00A0H.7       PUBLIC        RS
  C:180BH         PUBLIC        LCD9648_Init
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  B:0080H.0       PUBLIC        CS0
  C:000EH         PUBLIC        CN16CHARfont
  C:0250H         PUBLIC        EN16CHARfont
  B:00A0H.5       PUBLIC        SDA
  C:1338H         PUBLIC        _LCD9648_Write16CnCHAR
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  10:52:21  PAGE 14


  C:14AEH         PUBLIC        _LCD9648_Write16EnCHAR
  B:00A0H.6       PUBLIC        SCL
  D:00C8H         PUBLIC        T2CON
  C:1AC4H         PUBLIC        _SendDataSPI
  B:0080H.1       PUBLIC        RST
  C:19EAH         PUBLIC        LCD9648_Clear
  D:00D0H         PUBLIC        PSW
  -------         PROC          _SENDDATASPI
  D:0007H         SYMBOL        dat
  -------         DO            
  D:0006H         SYMBOL        i
  -------         ENDDO         
  C:1AC4H         LINE#         4
  C:1AC4H         LINE#         5
  C:1AC4H         LINE#         8
  C:1AC6H         LINE#         9
  C:1AC6H         LINE#         10
  C:1ACEH         LINE#         11
  C:1AD0H         LINE#         13
  C:1AD4H         LINE#         15
  C:1AD6H         LINE#         16
  C:1AD8H         LINE#         17
  C:1ADCH         LINE#         18
  -------         ENDPROC       _SENDDATASPI
  C:1B1EH         SYMBOL        L?0067
  C:1B20H         SYMBOL        L?0068
  -------         PROC          L?0066
  -------         ENDPROC       L?0066
  C:1B1EH         SYMBOL        L?0067
  C:1B20H         SYMBOL        L?0068
  -------         PROC          _WRITECOMM
  D:0006H         SYMBOL        i
  C:1B24H         LINE#         20
  C:1B24H         LINE#         21
  C:1B24H         LINE#         23
  C:1B26H         LINE#         24
  C:1B28H         LINE#         26
  C:1B2BH         LINE#         28
  C:1B2DH         LINE#         30
  -------         ENDPROC       _WRITECOMM
  C:1A7AH         SYMBOL        L?0070
  -------         PROC          L?0069
  -------         ENDPROC       L?0069
  C:1A7AH         SYMBOL        L?0070
  -------         PROC          _WRITEDATA
  D:0006H         SYMBOL        i
  C:1A86H         LINE#         31
  C:1A86H         LINE#         32
  C:1A86H         LINE#         33
  C:1A88H         LINE#         34
  C:1A8AH         LINE#         36
  C:1A8DH         LINE#         38
  C:1A8FH         LINE#         39
  -------         ENDPROC       _WRITEDATA
  -------         PROC          LCD9648_INIT
  -------         DO            
  D:0006H         SYMBOL        i
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  10:52:21  PAGE 15


  -------         ENDDO         
  C:180BH         LINE#         41
  C:180BH         LINE#         42
  C:180BH         LINE#         46
  C:180DH         LINE#         47
  C:181BH         LINE#         49
  C:181DH         LINE#         50
  C:182BH         LINE#         52
  C:182DH         LINE#         53
  C:183BH         LINE#         55
  C:1842H         LINE#         56
  C:1849H         LINE#         57
  C:1850H         LINE#         58
  C:1857H         LINE#         59
  C:185EH         LINE#         60
  C:1865H         LINE#         61
  C:186CH         LINE#         62
  -------         ENDPROC       LCD9648_INIT
  -------         PROC          LCD9648_CLEAR
  -------         DO            
  D:0005H         SYMBOL        i
  D:0004H         SYMBOL        j
  -------         ENDDO         
  C:19EAH         LINE#         67
  C:19EAH         LINE#         68
  C:19EAH         LINE#         71
  C:19ECH         LINE#         72
  C:19ECH         LINE#         73
  C:19F3H         LINE#         74
  C:19F9H         LINE#         75
  C:1A00H         LINE#         76
  C:1A06H         LINE#         78
  C:1A08H         LINE#         79
  C:1A08H         LINE#         80
  C:1A0EH         LINE#         81
  C:1A12H         LINE#         82
  C:1A16H         LINE#         83
  -------         ENDPROC       LCD9648_CLEAR
  -------         PROC          _LCD9648_WRITE16CNCHAR
  D:0062H         SYMBOL        x
  D:0063H         SYMBOL        y
  D:0001H         SYMBOL        cn
  -------         DO            
  D:0004H         SYMBOL        j
  D:0064H         SYMBOL        x1
  D:0065H         SYMBOL        x2
  D:0005H         SYMBOL        wordNum
  -------         ENDDO         
  C:1338H         LINE#         86
  C:133CH         LINE#         87
  C:133CH         LINE#         91
  C:1343H         LINE#         92
  C:1343H         LINE#         93
  C:1346H         LINE#         94
  C:1346H         LINE#         97
  C:134DH         LINE#         98
  C:134DH         LINE#         99
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  10:52:21  PAGE 16


  C:1350H         LINE#         100
  C:1350H         LINE#         101
  C:1356H         LINE#         103
  C:135CH         LINE#         104
  C:1364H         LINE#         105
  C:1364H         LINE#         108
  C:136BH         LINE#         110
  C:1372H         LINE#         111
  C:1378H         LINE#         113
  C:137BH         LINE#         114
  C:1382H         LINE#         115
  C:1384H         LINE#         116
  C:1384H         LINE#         118
  C:13B7H         LINE#         120
  C:13B7H         LINE#         121
  C:13B8H         LINE#         122
  C:13B8H         LINE#         123
  C:13BDH         LINE#         124
  C:13BDH         LINE#         126
  C:13C4H         LINE#         129
  C:13C7H         LINE#         130
  C:13CEH         LINE#         131
  C:13CEH         LINE#         132
  C:13DEH         LINE#         133
  C:13E2H         LINE#         134
  C:13E8H         LINE#         135
  C:13E8H         LINE#         136
  C:13EEH         LINE#         137
  C:13F5H         LINE#         139
  C:13F8H         LINE#         140
  C:13FAH         LINE#         141
  -------         ENDPROC       _LCD9648_WRITE16CNCHAR
  -------         PROC          _LCD9648_WRITE16ENCHAR
  D:0075H         SYMBOL        x
  D:0076H         SYMBOL        y
  D:0001H         SYMBOL        en
  -------         DO            
  D:0004H         SYMBOL        j
  D:0077H         SYMBOL        x1
  D:0078H         SYMBOL        x2
  D:0005H         SYMBOL        wordNum
  -------         ENDDO         
  C:14AEH         LINE#         143
  C:14B2H         LINE#         144
  C:14B2H         LINE#         148
  C:14B9H         LINE#         149
  C:14B9H         LINE#         150
  C:14BCH         LINE#         151
  C:14BCH         LINE#         154
  C:14C3H         LINE#         155
  C:14C3H         LINE#         156
  C:14C6H         LINE#         157
  C:14C6H         LINE#         158
  C:14CCH         LINE#         160
  C:14D2H         LINE#         161
  C:14DAH         LINE#         162
  C:14DAH         LINE#         165
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  10:52:21  PAGE 17


  C:14E1H         LINE#         167
  C:14E8H         LINE#         168
  C:14EEH         LINE#         170
  C:14F3H         LINE#         171
  C:14FAH         LINE#         172
  C:14FCH         LINE#         173
  C:14FCH         LINE#         175
  C:1514H         LINE#         176
  C:1514H         LINE#         177
  C:1515H         LINE#         178
  C:1515H         LINE#         179
  C:151AH         LINE#         180
  C:151AH         LINE#         182
  C:1521H         LINE#         185
  C:1526H         LINE#         186
  C:152DH         LINE#         187
  C:152DH         LINE#         188
  C:153DH         LINE#         189
  C:1541H         LINE#         190
  C:1547H         LINE#         191
  C:1547H         LINE#         192
  C:154DH         LINE#         193
  C:1554H         LINE#         195
  C:1557H         LINE#         196
  C:1559H         LINE#         197
  -------         ENDPROC       _LCD9648_WRITE16ENCHAR
  -------         PROC          _LCD9648_WRITE16ENCHAR1
  D:0001H         SYMBOL        x
  D:0005H         SYMBOL        y
  D:0003H         SYMBOL        en
  -------         DO            
  D:0002H         SYMBOL        j
  D:0014H         SYMBOL        x1
  D:0015H         SYMBOL        x2
  D:0004H         SYMBOL        wordNum
  -------         ENDDO         
  C:1680H         LINE#         198
  C:1682H         LINE#         199
  C:1682H         LINE#         203
  C:1688H         LINE#         204
  C:1688H         LINE#         205
  C:168BH         LINE#         206
  C:168BH         LINE#         209
  C:1691H         LINE#         210
  C:1691H         LINE#         211
  C:1694H         LINE#         212
  C:1694H         LINE#         213
  C:1698H         LINE#         215
  C:169EH         LINE#         217
  C:169EH         LINE#         220
  C:16A5H         LINE#         222
  C:16ABH         LINE#         223
  C:16B0H         LINE#         225
  C:16B5H         LINE#         226
  C:16BCH         LINE#         227
  C:16BEH         LINE#         228
  C:16BEH         LINE#         230
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  10:52:21  PAGE 18


  C:16D2H         LINE#         231
  C:16D2H         LINE#         232
  C:16D3H         LINE#         233
  C:16D3H         LINE#         234
  C:16D8H         LINE#         235
  C:16D8H         LINE#         237
  C:16DEH         LINE#         240
  C:16E3H         LINE#         241
  C:16EAH         LINE#         242
  C:16EAH         LINE#         243
  C:16FFH         LINE#         244
  C:1703H         LINE#         245
  C:1707H         LINE#         246
  C:1707H         LINE#         247
  C:170BH         LINE#         248
  C:170BH         LINE#         250
  C:170BH         LINE#         251
  C:170DH         LINE#         252
  -------         ENDPROC       _LCD9648_WRITE16ENCHAR1
  -------         ENDMOD        LCD9648

  -------         MODULE        KEY
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  C:1ADDH         PUBLIC        Key_Read
  D:00A8H         PUBLIC        IE
  D:00B8H         PUBLIC        IP
  B:0090H.0       PUBLIC        k1
  B:0090H.1       PUBLIC        k2
  B:0090H.2       PUBLIC        k3
  B:0090H.3       PUBLIC        k4
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  D:00C8H         PUBLIC        T2CON
  D:00D0H         PUBLIC        PSW
  -------         PROC          KEY_READ
  -------         DO            
  D:0007H         SYMBOL        temp
  -------         ENDDO         
  C:1ADDH         LINE#         4
  C:1ADDH         LINE#         5
  C:1ADDH         LINE#         6
  C:1ADFH         LINE#         8
  C:1AE3H         LINE#         9
  C:1AE8H         LINE#         10
  C:1AEDH         LINE#         11
  C:1AF2H         LINE#         12
  C:1AF2H         LINE#         13
  -------         ENDPROC       KEY_READ
  -------         ENDMOD        KEY

  -------         MODULE        RTC3085
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  D:00A8H         PUBLIC        IE
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  10:52:21  PAGE 19


  C:194EH         PUBLIC        _Ds1302Write
  C:1AF3H         PUBLIC        Ds1302ReadTime
  C:1A17H         PUBLIC        Ds1302Init
  D:00B8H         PUBLIC        IP
  B:00B0H.6       PUBLIC        SCLK
  B:00B0H.4       PUBLIC        DSIO
  D:000EH         PUBLIC        TIME
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  C:1B60H         PUBLIC        WRITE_RTC_ADDR
  C:1B5AH         PUBLIC        READ_RTC_ADDR
  D:00C8H         PUBLIC        T2CON
  B:00B0H.5       PUBLIC        RST
  D:00D0H         PUBLIC        PSW
  C:18C9H         PUBLIC        _Ds1302Read
  -------         PROC          _DS1302WRITE
  D:0007H         SYMBOL        addr
  D:0005H         SYMBOL        dat
  -------         DO            
  D:0006H         SYMBOL        n
  -------         ENDDO         
  C:194EH         LINE#         25
  C:194EH         LINE#         26
  C:194EH         LINE#         28
  C:1950H         LINE#         29
  C:1951H         LINE#         31
  C:1953H         LINE#         32
  C:1954H         LINE#         33
  C:1956H         LINE#         34
  C:1957H         LINE#         36
  C:1959H         LINE#         37
  C:1959H         LINE#         38
  C:195DH         LINE#         39
  C:1961H         LINE#         40
  C:1963H         LINE#         41
  C:1964H         LINE#         42
  C:1966H         LINE#         43
  C:1967H         LINE#         44
  C:196BH         LINE#         45
  C:196DH         LINE#         46
  C:196DH         LINE#         47
  C:1971H         LINE#         48
  C:1975H         LINE#         49
  C:1977H         LINE#         50
  C:1978H         LINE#         51
  C:197AH         LINE#         52
  C:197BH         LINE#         53
  C:197FH         LINE#         55
  C:1981H         LINE#         56
  C:1982H         LINE#         57
  -------         ENDPROC       _DS1302WRITE
  -------         PROC          _DS1302READ
  D:0007H         SYMBOL        addr
  -------         DO            
  D:0005H         SYMBOL        n
  D:0006H         SYMBOL        dat
  D:0007H         SYMBOL        dat1
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  10:52:21  PAGE 20


  -------         ENDDO         
  C:18C9H         LINE#         66
  C:18C9H         LINE#         67
  C:18C9H         LINE#         69
  C:18CBH         LINE#         70
  C:18CCH         LINE#         72
  C:18CEH         LINE#         73
  C:18CFH         LINE#         74
  C:18D1H         LINE#         75
  C:18D2H         LINE#         77
  C:18D4H         LINE#         78
  C:18D4H         LINE#         79
  C:18D8H         LINE#         80
  C:18DCH         LINE#         81
  C:18DEH         LINE#         82
  C:18DFH         LINE#         83
  C:18E1H         LINE#         84
  C:18E2H         LINE#         85
  C:18E6H         LINE#         86
  C:18E7H         LINE#         87
  C:18E9H         LINE#         88
  C:18E9H         LINE#         89
  C:18EEH         LINE#         90
  C:18FAH         LINE#         91
  C:18FCH         LINE#         92
  C:18FDH         LINE#         93
  C:18FFH         LINE#         94
  C:1900H         LINE#         95
  C:1904H         LINE#         97
  C:1906H         LINE#         98
  C:1907H         LINE#         99
  C:1909H         LINE#         100
  C:190AH         LINE#         101
  C:190CH         LINE#         102
  C:190DH         LINE#         103
  C:190FH         LINE#         104
  C:1910H         LINE#         105
  C:1912H         LINE#         106
  -------         ENDPROC       _DS1302READ
  -------         PROC          DS1302INIT
  -------         DO            
  D:0004H         SYMBOL        n
  -------         ENDDO         
  C:1A17H         LINE#         115
  C:1A17H         LINE#         116
  C:1A17H         LINE#         118
  C:1A1EH         LINE#         119
  C:1A20H         LINE#         120
  C:1A20H         LINE#         121
  C:1A2FH         LINE#         122
  C:1A33H         LINE#         123
  -------         ENDPROC       DS1302INIT
  -------         PROC          DS1302READTIME
  -------         DO            
  D:0003H         SYMBOL        n
  -------         ENDDO         
  C:1AF3H         LINE#         133
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  10:52:21  PAGE 21


  C:1AF3H         LINE#         134
  C:1AF3H         LINE#         136
  C:1AF5H         LINE#         137
  C:1AF5H         LINE#         138
  C:1B04H         LINE#         139
  C:1B08H         LINE#         141
  -------         ENDPROC       DS1302READTIME
  -------         ENDMOD        RTC3085

  -------         MODULE        ?C?FPADD
  C:08A6H         PUBLIC        ?C?FPADD
  C:08A2H         PUBLIC        ?C?FPSUB
  -------         ENDMOD        ?C?FPADD

  -------         MODULE        ?C?FPMUL
  C:0997H         PUBLIC        ?C?FPMUL
  -------         ENDMOD        ?C?FPMUL

  -------         MODULE        ?C?FPCMP
  C:0A9FH         PUBLIC        ?C?FPCMP
  C:0A9DH         PUBLIC        ?C?FPCMP3
  -------         ENDMOD        ?C?FPCMP

  -------         MODULE        ?C?FCAST
  C:0B20H         PUBLIC        ?C?FCASTC
  C:0B1BH         PUBLIC        ?C?FCASTI
  C:0B16H         PUBLIC        ?C?FCASTL
  -------         ENDMOD        ?C?FCAST

  -------         MODULE        ?C?CASTF
  C:0B54H         PUBLIC        ?C?CASTF
  -------         ENDMOD        ?C?CASTF

  -------         MODULE        ?C?CLDPTR
  C:0BD5H         PUBLIC        ?C?CLDPTR
  -------         ENDMOD        ?C?CLDPTR

  -------         MODULE        ?C?CLDOPTR
  C:0BEEH         PUBLIC        ?C?CLDOPTR
  -------         ENDMOD        ?C?CLDOPTR

  -------         MODULE        ?C?CSTPTR
  C:0C1BH         PUBLIC        ?C?CSTPTR
  -------         ENDMOD        ?C?CSTPTR

  -------         MODULE        ?C?CSTOPTR
  C:0C2DH         PUBLIC        ?C?CSTOPTR
  -------         ENDMOD        ?C?CSTOPTR

  -------         MODULE        ?C?LNEG
  C:0C4FH         PUBLIC        ?C?LNEG
  -------         ENDMOD        ?C?LNEG

  -------         MODULE        ?C?LLDIDATA
  C:0C5DH         PUBLIC        ?C?LLDIDATA
  -------         ENDMOD        ?C?LLDIDATA

BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  10:52:21  PAGE 22


  -------         MODULE        ?C?LSTIDATA
  C:0C69H         PUBLIC        ?C?LSTIDATA
  -------         ENDMOD        ?C?LSTIDATA

  -------         MODULE        ?C?LSTKIDATA
  C:0C75H         PUBLIC        ?C?LSTKIDATA
  -------         ENDMOD        ?C?LSTKIDATA

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?DS18B20_INIT?DS18B20

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?DS18B20_READ_TEMPERTURE?DS18B20

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?_LCD9648_WRITE16ENCHAR1?LCD9648

Program Size: data=116.1 xdata=0 code=7021
LINK/LOCATE RUN COMPLETE.  3 WARNING(S),  0 ERROR(S)
