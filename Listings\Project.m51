BL<PERSON> BANKED LINKER/LOCATER V6.22                                                        07/08/2025  10:34:31  PAGE 1


BL51 BANKED LINKER/LOCATER V6.22, INVOKED BY:
E:\KEIL\C51\BIN\BL51.EXE .\Objects\main.obj, .\Objects\ds18b20.obj, .\Objects\lcd9648.obj, .\Objects\Key.obj, .\Objects\
>> rtc3085.obj TO .\Objects\Project PRINT (.\Listings\Project.m51) RAMSIZE (256)


MEMORY MODEL: SMALL WITH FLOATING POINT ARITHMETIC


INPUT MODULES INCLUDED:
  .\Objects\main.obj (MAIN)
  .\Objects\ds18b20.obj (DS18B20)
  .\Objects\lcd9648.obj (LCD9648)
  .\Objects\Key.obj (KEY)
  .\Objects\rtc3085.obj (RTC3085)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?FPADD)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?FPMUL)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?FPCMP)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?FCAST)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?CASTF)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?FPGETOPN)
  E:\KEIL\C51\LIB\C51S.LIB (?C_STARTUP)
  E:\KEIL\C51\LIB\C51S.LIB (?C?CLDPTR)
  E:\KEIL\C51\LIB\C51S.LIB (?C?CLDOPTR)
  E:\KEIL\C51\LIB\C51S.LIB (?C?CSTPTR)
  E:\KEIL\C51\LIB\C51S.LIB (?C?CSTOPTR)
  E:\KEIL\C51\LIB\C51S.LIB (?C?LNEG)
  E:\KEIL\C51\LIB\C51S.LIB (?C?LLDIDATA)
  E:\KEIL\C51\LIB\C51S.LIB (?C?LSTIDATA)
  E:\KEIL\C51\LIB\C51S.LIB (?C?LSTKIDATA)
  E:\KEIL\C51\LIB\C51S.LIB (?C_INIT)


LINK MAP OF MODULE:  .\Objects\Project (MAIN)


            TYPE    BASE      LENGTH    RELOCATION   SEGMENT NAME
            -----------------------------------------------------

            * * * * * * *   D A T A   M E M O R Y   * * * * * * *
            REG     0000H     0008H     ABSOLUTE     "REG BANK 0"
            DATA    0008H     0006H     UNIT         ?DT?DS18B20_READ_TEMPERTURE?DS18B20
            DATA    000EH     0006H     UNIT         ?DT?RTC3085
            DATA    0014H     0002H     UNIT         ?DT?_LCD9648_WRITE16ENCHAR1?LCD9648
                    0016H     000AH                  *** GAP ***
            BIT     0020H.0   0000H.1   UNIT         ?BI?MAIN
                    0020H.1   0000H.7                *** GAP ***
            DATA    0021H     0040H     UNIT         ?DT?MAIN
            DATA    0061H     001BH     UNIT         _DATA_GROUP_
            IDATA   007CH     0001H     UNIT         ?STACK

            * * * * * * *   C O D E   M E M O R Y   * * * * * * *
            CODE    0000H     0003H     ABSOLUTE     
            CODE    0003H     0008H     UNIT         ?PR?DS18B20_START?DS18B20
            CODE    000BH     0003H     ABSOLUTE     
            CODE    000EH     0891H     UNIT         ?CO?LCD9648
            CODE    089FH     03EFH     UNIT         ?C?LIB_CODE
            CODE    0C8EH     0277H     UNIT         ?PR?LCD_PROC?MAIN
            CODE    0F05H     016CH     UNIT         ?PR?LCD9648_INIT_PROC?MAIN
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  10:34:31  PAGE 2


            CODE    1071H     0137H     UNIT         ?PR?KEY_PROC?MAIN
            CODE    11A8H     00C3H     UNIT         ?PR?_LCD9648_WRITE16CNCHAR?LCD9648
            CODE    126BH     00B3H     UNIT         ?PR?_TIME_TO_STRING?MAIN
            CODE    131EH     00ACH     UNIT         ?PR?_LCD9648_WRITE16ENCHAR?LCD9648
            CODE    13CAH     0095H     UNIT         ?CO?MAIN
            CODE    145FH     0091H     UNIT         ?PR?_FLOAT_TO_STRING?MAIN
            CODE    14F0H     008EH     UNIT         ?PR?_LCD9648_WRITE16ENCHAR1?LCD9648
            CODE    157EH     008CH     UNIT         ?C_C51STARTUP
            CODE    160AH     0071H     UNIT         ?PR?DS18B20_READ_TEMPERTURE?DS18B20
            CODE    167BH     0068H     UNIT         ?PR?LCD9648_INIT?LCD9648
            CODE    16E3H     0053H     UNIT         ?C_INITSEG
            CODE    1736H     004AH     UNIT         ?PR?_DS1302READ?RTC3085
            CODE    1780H     003BH     UNIT         ?PR?DS18B20_CHECK?DS18B20
            CODE    17BBH     0035H     UNIT         ?PR?_DS1302WRITE?RTC3085
            CODE    17F0H     0034H     UNIT         ?PR?_DS18B20_WRITE_BYTE?DS18B20
            CODE    1824H     0033H     UNIT         ?PR?TIMER0_ISR?MAIN
            CODE    1857H     002DH     UNIT         ?PR?LCD9648_CLEAR?LCD9648
            CODE    1884H     0023H     UNIT         ?PR?DS1302INIT?RTC3085
            CODE    18A7H     0020H     UNIT         ?PR?LCD_INIT_TEST?MAIN
            CODE    18C7H     0020H     UNIT         ?PR?MAIN?MAIN
            CODE    18E7H     001BH     UNIT         ?PR?DS18B20_READ_BYTE?DS18B20
            CODE    1902H     001BH     UNIT         ?PR?_WRITEDATA?LCD9648
            CODE    191DH     001AH     UNIT         ?PR?DS18B20_READ_BIT?DS18B20
            CODE    1937H     0019H     UNIT         ?PR?_SENDDATASPI?LCD9648
            CODE    1950H     0016H     UNIT         ?PR?KEY_READ?KEY
            CODE    1966H     0016H     UNIT         ?PR?DS1302READTIME?RTC3085
            CODE    197CH     0013H     UNIT         ?PR?TIMER0_INIT?MAIN
            CODE    198FH     0012H     UNIT         ?PR?_WRITECOMM?LCD9648
            CODE    19A1H     000FH     UNIT         ?PR?_BCD_TO_DEC?MAIN
            CODE    19B0H     000FH     UNIT         ?PR?_DELAY_10US?DS18B20
            CODE    19BFH     000EH     UNIT         ?PR?DS18B20_RESET?DS18B20
            CODE    19CDH     000CH     UNIT         ?CO?RTC3085
            CODE    19D9H     0007H     UNIT         ?PR?DS18B20_INIT?DS18B20



OVERLAY MAP OF MODULE:   .\Objects\Project (MAIN)


SEGMENT                                       DATA_GROUP 
  +--> CALLED SEGMENT                       START    LENGTH
-----------------------------------------------------------
?C_C51STARTUP                               -----    -----
  +--> ?PR?MAIN?MAIN
  +--> ?C_INITSEG

?PR?MAIN?MAIN                               -----    -----
  +--> ?PR?DS1302INIT?RTC3085
  +--> ?PR?LCD9648_INIT_PROC?MAIN
  +--> ?PR?TIMER0_INIT?MAIN
  +--> ?PR?LCD_INIT_TEST?MAIN
  +--> ?PR?KEY_PROC?MAIN
  +--> ?PR?LCD_PROC?MAIN

?PR?DS1302INIT?RTC3085                      -----    -----
  +--> ?PR?_DS1302WRITE?RTC3085
  +--> ?CO?RTC3085
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  10:34:31  PAGE 3



?PR?LCD9648_INIT_PROC?MAIN                  -----    -----
  +--> ?PR?LCD9648_INIT?LCD9648
  +--> ?PR?LCD9648_CLEAR?LCD9648
  +--> ?CO?MAIN
  +--> ?PR?_LCD9648_WRITE16CNCHAR?LCD9648
  +--> ?PR?_LCD9648_WRITE16ENCHAR?LCD9648

?PR?LCD9648_INIT?LCD9648                    -----    -----
  +--> ?PR?_WRITECOMM?LCD9648

?PR?_WRITECOMM?LCD9648                      -----    -----
  +--> ?PR?_SENDDATASPI?LCD9648

?PR?LCD9648_CLEAR?LCD9648                   -----    -----
  +--> ?PR?_WRITECOMM?LCD9648
  +--> ?PR?_WRITEDATA?LCD9648

?PR?_WRITEDATA?LCD9648                      -----    -----
  +--> ?PR?_SENDDATASPI?LCD9648

?PR?_LCD9648_WRITE16CNCHAR?LCD9648          0061H    0004H
  +--> ?PR?_WRITECOMM?LCD9648
  +--> ?CO?LCD9648
  +--> ?PR?_WRITEDATA?LCD9648

?PR?_LCD9648_WRITE16ENCHAR?LCD9648          0073H    0004H
  +--> ?PR?_WRITECOMM?LCD9648
  +--> ?CO?LCD9648
  +--> ?PR?_WRITEDATA?LCD9648

?PR?LCD_INIT_TEST?MAIN                      -----    -----
  +--> ?PR?LCD9648_INIT_PROC?MAIN

?PR?KEY_PROC?MAIN                           -----    -----
  +--> ?PR?KEY_READ?KEY

?PR?LCD_PROC?MAIN                           0061H    0012H
  +--> ?PR?LCD9648_CLEAR?LCD9648
  +--> ?CO?MAIN
  +--> ?PR?_LCD9648_WRITE16ENCHAR?LCD9648
  +--> ?PR?_FLOAT_TO_STRING?MAIN
  +--> ?PR?_TIME_TO_STRING?MAIN

?PR?_FLOAT_TO_STRING?MAIN                   0073H    0009H

?PR?_TIME_TO_STRING?MAIN                    0073H    0003H
  +--> ?PR?DS1302READTIME?RTC3085
  +--> ?PR?_BCD_TO_DEC?MAIN

?PR?DS1302READTIME?RTC3085                  -----    -----
  +--> ?CO?RTC3085
  +--> ?PR?_DS1302READ?RTC3085



SYMBOL TABLE OF MODULE:  .\Objects\Project (MAIN)
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  10:34:31  PAGE 4



  VALUE           TYPE          NAME
  ----------------------------------

  -------         MODULE        MAIN
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0080H         PUBLIC        P0
  D:0090H         PUBLIC        P1
  D:00A0H         PUBLIC        P2
  D:00B0H         PUBLIC        P3
  C:1824H         PUBLIC        Timer0_ISR
  B:00A8H.7       PUBLIC        EA
  D:00A8H         PUBLIC        IE
  C:126BH         PUBLIC        _Time_To_String
  D:0021H         PUBLIC        Temperature
  D:0022H         PUBLIC        Display_Buffer
  D:00B8H         PUBLIC        IP
  C:0F05H         PUBLIC        LCD9648_Init_Proc
  B:0020H.0       PUBLIC        LCD_Init_End_Flag
  C:1071H         PUBLIC        Key_Proc
  D:0032H         PUBLIC        Key_Down
  C:18C7H         PUBLIC        main
  C:197CH         PUBLIC        Timer0_Init
  D:0033H         PUBLIC        Key_Old
  D:0034H         PUBLIC        Key_Slow_Down
  D:0035H         PUBLIC        LCD_Disp_Mode
  D:0036H         PUBLIC        Key_Val
  D:0037H         PUBLIC        Last_Display_Page
  D:0038H         PUBLIC        Last_LCD_Mode
  D:0039H         PUBLIC        LCD_Init_Mode
  D:0098H         PUBLIC        SCON
  D:0089H         PUBLIC        TMOD
  D:0088H         PUBLIC        TCON
  D:003AH         PUBLIC        Last_Setting_Page
  D:003BH         PUBLIC        Display_Page
  D:003CH         PUBLIC        Setting_Index
  D:003DH         PUBLIC        Timer0_count
  D:003FH         PUBLIC        Setting_Page
  B:00A8H.1       PUBLIC        ET0
  C:0C8EH         PUBLIC        LCD_Proc
  D:008CH         PUBLIC        TH0
  C:18A7H         PUBLIC        LCD_Init_Test
  D:0040H         PUBLIC        Key_Up
  D:008AH         PUBLIC        TL0
  D:0041H         PUBLIC        Setting_Disp
  D:004DH         PUBLIC        Last_State_Index
  B:0088H.4       PUBLIC        TR0
  D:004EH         PUBLIC        Time_Flag_Count
  C:19A1H         PUBLIC        _BCD_To_Dec
  D:004FH         PUBLIC        State_Index
  D:00C8H         PUBLIC        T2CON
  D:0050H         PUBLIC        Time_Flag
  D:0051H         PUBLIC        State_Disp
  C:1470H         PUBLIC        _Float_To_String
  D:00D0H         PUBLIC        PSW
  -------         PROC          _BCD_TO_DEC
  D:0007H         SYMBOL        bcd
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  10:34:31  PAGE 5


  C:19A1H         LINE#         35
  -------         ENDPROC       _BCD_TO_DEC
  -------         PROC          KEY_PROC
  C:1071H         LINE#         39
  C:1071H         LINE#         40
  C:1071H         LINE#         41
  C:1078H         LINE#         42
  C:107BH         LINE#         44
  C:1080H         LINE#         45
  C:1089H         LINE#         46
  C:108FH         LINE#         47
  C:1092H         LINE#         49
  C:10ABH         LINE#         50
  C:10ABH         LINE#         51
  C:10ABH         LINE#         52
  C:10B4H         LINE#         53
  C:10B4H         LINE#         54
  C:10BBH         LINE#         55
  C:10BBH         LINE#         57
  C:10BBH         LINE#         59
  C:10BBH         LINE#         60
  C:10D9H         LINE#         61
  C:10F5H         LINE#         62
  C:1105H         LINE#         63
  C:1105H         LINE#         64
  C:1105H         LINE#         65
  C:1106H         LINE#         67
  C:1106H         LINE#         68
  C:110FH         LINE#         69
  C:110FH         LINE#         70
  C:1116H         LINE#         71
  C:1116H         LINE#         73
  C:1116H         LINE#         75
  C:1116H         LINE#         76
  C:1134H         LINE#         77
  C:114CH         LINE#         78
  C:115CH         LINE#         79
  C:115CH         LINE#         80
  C:115CH         LINE#         81
  C:115DH         LINE#         83
  C:115DH         LINE#         84
  C:1161H         LINE#         85
  C:1161H         LINE#         86
  C:116DH         LINE#         87
  C:116EH         LINE#         88
  C:1174H         LINE#         89
  C:1174H         LINE#         90
  C:1180H         LINE#         91
  C:1188H         LINE#         92
  C:1188H         LINE#         93
  C:1189H         LINE#         95
  C:1189H         LINE#         96
  C:118EH         LINE#         97
  C:118EH         LINE#         98
  C:1192H         LINE#         99
  C:1192H         LINE#         100
  C:119CH         LINE#         101
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  10:34:31  PAGE 6


  C:119DH         LINE#         103
  C:119DH         LINE#         104
  C:11A7H         LINE#         105
  C:11A7H         LINE#         106
  C:11A7H         LINE#         107
  C:11A7H         LINE#         108
  C:11A7H         LINE#         109
  -------         ENDPROC       KEY_PROC
  -------         PROC          _TIME_TO_STRING
  D:0073H         SYMBOL        time_str
  C:126BH         LINE#         112
  C:1271H         LINE#         113
  C:1271H         LINE#         114
  C:1274H         LINE#         117
  C:1289H         LINE#         118
  C:12A3H         LINE#         119
  C:12ABH         LINE#         120
  C:12C3H         LINE#         121
  C:12DDH         LINE#         122
  C:12E5H         LINE#         123
  C:12FDH         LINE#         124
  C:1317H         LINE#         125
  -------         ENDPROC       _TIME_TO_STRING
  C:1467H         SYMBOL        L?0082
  -------         PROC          L?0081
  -------         ENDPROC       L?0081
  C:1467H         SYMBOL        L?0082
  -------         PROC          _FLOAT_TO_STRING
  D:0073H         SYMBOL        value
  D:0077H         SYMBOL        str
  D:007AH         SYMBOL        unit
  -------         DO            
  D:007BH         SYMBOL        integer_part
  D:0007H         SYMBOL        decimal_part
  -------         ENDDO         
  C:1470H         LINE#         129
  C:147EH         LINE#         130
  C:147EH         LINE#         131
  C:1483H         LINE#         132
  C:14A9H         LINE#         134
  C:14BAH         LINE#         135
  C:14D0H         LINE#         136
  C:14D8H         LINE#         137
  C:14E1H         LINE#         138
  C:14E9H         LINE#         139
  -------         ENDPROC       _FLOAT_TO_STRING
  -------         PROC          TIMER0_INIT
  C:197CH         LINE#         146
  C:197CH         LINE#         147
  C:197CH         LINE#         148
  C:197FH         LINE#         149
  C:1982H         LINE#         150
  C:1985H         LINE#         151
  C:1988H         LINE#         152
  C:198AH         LINE#         153
  C:198CH         LINE#         154
  C:198EH         LINE#         155
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  10:34:31  PAGE 7


  -------         ENDPROC       TIMER0_INIT
  -------         PROC          TIMER0_ISR
  C:1824H         LINE#         161
  C:1828H         LINE#         163
  C:182BH         LINE#         164
  C:182EH         LINE#         166
  C:1836H         LINE#         168
  C:183FH         LINE#         169
  C:183FH         LINE#         170
  C:1842H         LINE#         171
  C:1848H         LINE#         173
  C:1848H         LINE#         175
  C:1852H         LINE#         176
  -------         ENDPROC       TIMER0_ISR
  -------         PROC          LCD9648_INIT_PROC
  C:0F05H         LINE#         177
  C:0F05H         LINE#         178
  C:0F05H         LINE#         179
  C:0F1BH         LINE#         180
  C:0F1BH         LINE#         181
  C:0F1BH         LINE#         182
  C:0F1EH         LINE#         183
  C:0F21H         LINE#         185
  C:0F2DH         LINE#         186
  C:0F3AH         LINE#         187
  C:0F47H         LINE#         189
  C:0F54H         LINE#         191
  C:0F61H         LINE#         192
  C:0F6EH         LINE#         193
  C:0F7BH         LINE#         194
  C:0F88H         LINE#         195
  C:0F95H         LINE#         196
  C:0F95H         LINE#         198
  C:0F95H         LINE#         199
  C:0F98H         LINE#         200
  C:0F9BH         LINE#         202
  C:0FA7H         LINE#         203
  C:0FB4H         LINE#         204
  C:0FC1H         LINE#         205
  C:0FCEH         LINE#         206
  C:0FDBH         LINE#         207
  C:0FE8H         LINE#         209
  C:0FF5H         LINE#         210
  C:1002H         LINE#         211
  C:100FH         LINE#         212
  C:101CH         LINE#         213
  C:1029H         LINE#         214
  C:1036H         LINE#         216
  C:1043H         LINE#         217
  C:1050H         LINE#         218
  C:105DH         LINE#         219
  C:106AH         LINE#         220
  C:106AH         LINE#         222
  C:106AH         LINE#         223
  C:106DH         LINE#         224
  C:1070H         LINE#         232
  C:1070H         LINE#         234
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  10:34:31  PAGE 8


  C:1070H         LINE#         236
  C:1070H         LINE#         237
  C:1070H         LINE#         238
  -------         ENDPROC       LCD9648_INIT_PROC
  -------         PROC          LCD_INIT_TEST
  C:18A7H         LINE#         240
  C:18A7H         LINE#         241
  C:18A7H         LINE#         242
  C:18ABH         LINE#         243
  C:18ABH         LINE#         244
  C:18AEH         LINE#         245
  C:18B0H         LINE#         246
  C:18B2H         LINE#         247
  C:18B9H         LINE#         248
  C:18B9H         LINE#         249
  C:18BCH         LINE#         250
  C:18BCH         LINE#         251
  C:18C1H         LINE#         252
  C:18C6H         LINE#         253
  C:18C6H         LINE#         255
  -------         ENDPROC       LCD_INIT_TEST
  -------         PROC          LCD_PROC
  -------         DO            
  D:0061H         SYMBOL        temp_str
  D:0071H         SYMBOL        i
  D:0072H         SYMBOL        need_update
  -------         ENDDO         
  C:0C8EH         LINE#         257
  C:0C8EH         LINE#         258
  C:0C8EH         LINE#         261
  C:0C91H         LINE#         263
  C:0C97H         LINE#         266
  C:0CB5H         LINE#         269
  C:0CB5H         LINE#         270
  C:0CB8H         LINE#         271
  C:0CBBH         LINE#         272
  C:0CBEH         LINE#         273
  C:0CC1H         LINE#         274
  C:0CC1H         LINE#         276
  C:0CC8H         LINE#         277
  C:0CC8H         LINE#         278
  C:0CCFH         LINE#         279
  C:0CCFH         LINE#         280
  C:0CD3H         LINE#         281
  C:0CD3H         LINE#         282
  C:0CE0H         LINE#         283
  C:0CECH         LINE#         284
  C:0CF9H         LINE#         285
  C:0D06H         LINE#         286
  C:0D06H         LINE#         288
  C:0D09H         LINE#         289
  C:0D16H         LINE#         291
  C:0D21H         LINE#         292
  C:0D2EH         LINE#         294
  C:0D42H         LINE#         295
  C:0D4AH         LINE#         296
  C:0D4CH         LINE#         298
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  10:34:31  PAGE 9


  C:0D4CH         LINE#         299
  C:0D50H         LINE#         300
  C:0D50H         LINE#         301
  C:0D5DH         LINE#         302
  C:0D6AH         LINE#         303
  C:0D6AH         LINE#         305
  C:0D73H         LINE#         306
  C:0D7DH         LINE#         307
  C:0D7DH         LINE#         308
  C:0D80H         LINE#         309
  C:0D89H         LINE#         310
  C:0D89H         LINE#         311
  C:0D90H         LINE#         312
  C:0D90H         LINE#         313
  C:0D94H         LINE#         314
  C:0D94H         LINE#         315
  C:0DA1H         LINE#         317
  C:0DA4H         LINE#         318
  C:0DA4H         LINE#         319
  C:0DA9H         LINE#         320
  C:0DB4H         LINE#         322
  C:0DC4H         LINE#         323
  C:0DCDH         LINE#         325
  C:0DDAH         LINE#         326
  C:0DE7H         LINE#         327
  C:0DF4H         LINE#         328
  C:0DF4H         LINE#         330
  C:0DF7H         LINE#         331
  C:0E04H         LINE#         333
  C:0E0FH         LINE#         334
  C:0E1CH         LINE#         336
  C:0E30H         LINE#         337
  C:0E3AH         LINE#         338
  C:0E3DH         LINE#         340
  C:0E3DH         LINE#         341
  C:0E41H         LINE#         342
  C:0E41H         LINE#         343
  C:0E4EH         LINE#         345
  C:0E51H         LINE#         346
  C:0E51H         LINE#         347
  C:0E56H         LINE#         348
  C:0E61H         LINE#         350
  C:0E71H         LINE#         351
  C:0E7AH         LINE#         353
  C:0E87H         LINE#         354
  C:0E94H         LINE#         355
  C:0EA1H         LINE#         356
  C:0EA1H         LINE#         358
  C:0EB5H         LINE#         359
  C:0EC2H         LINE#         361
  C:0ED6H         LINE#         362
  C:0EE3H         LINE#         364
  C:0EF7H         LINE#         365
  C:0F04H         LINE#         366
  C:0F04H         LINE#         367
  C:0F04H         LINE#         368
  -------         ENDPROC       LCD_PROC
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  10:34:31  PAGE 10


  -------         PROC          MAIN
  C:18C7H         LINE#         371
  C:18C7H         LINE#         372
  C:18C7H         LINE#         374
  C:18CAH         LINE#         375
  C:18CDH         LINE#         376
  C:18D0H         LINE#         377
  C:18D3H         LINE#         380
  C:18D6H         LINE#         382
  C:18D9H         LINE#         383
  C:18DCH         LINE#         385
  C:18DCH         LINE#         386
  C:18DCH         LINE#         387
  C:18DFH         LINE#         388
  C:18E2H         LINE#         389
  C:18E5H         LINE#         390
  -------         ENDPROC       MAIN
  -------         ENDMOD        MAIN

  -------         MODULE        DS18B20
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  C:19BFH         PUBLIC        ds18b20_reset
  D:00A8H         PUBLIC        IE
  C:0003H         PUBLIC        ds18b20_start
  D:00B8H         PUBLIC        IP
  C:18E7H         PUBLIC        ds18b20_read_byte
  C:19D9H         PUBLIC        ds18b20_init
  C:191DH         PUBLIC        ds18b20_read_bit
  C:17F8H         PUBLIC        _ds18b20_write_byte
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  C:160AH         PUBLIC        ds18b20_read_temperture
  C:19B0H         PUBLIC        _delay_10us
  B:00B0H.7       PUBLIC        DS18B20_PORT
  D:00C8H         PUBLIC        T2CON
  C:1780H         PUBLIC        ds18b20_check
  D:00D0H         PUBLIC        PSW
  -------         PROC          _DELAY_10US
  D:0007H         SYMBOL        xms
  -------         DO            
  D:0006H         SYMBOL        i
  -------         ENDDO         
  C:19B0H         LINE#         4
  C:19B0H         LINE#         5
  C:19B0H         LINE#         7
  C:19B6H         LINE#         8
  C:19B6H         LINE#         9
  C:19B8H         LINE#         10
  C:19BEH         LINE#         12
  -------         ENDPROC       _DELAY_10US
  -------         PROC          DS18B20_RESET
  C:19BFH         LINE#         20
  C:19BFH         LINE#         21
  C:19BFH         LINE#         22
  C:19C1H         LINE#         23
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  10:34:31  PAGE 11


  C:19C6H         LINE#         24
  C:19C8H         LINE#         25
  -------         ENDPROC       DS18B20_RESET
  -------         PROC          DS18B20_CHECK
  -------         DO            
  D:0004H         SYMBOL        time_temp
  -------         ENDDO         
  C:1780H         LINE#         34
  C:1780H         LINE#         35
  C:1780H         LINE#         36
  C:1782H         LINE#         38
  C:178BH         LINE#         39
  C:178BH         LINE#         40
  C:178CH         LINE#         41
  C:1791H         LINE#         42
  C:1793H         LINE#         43
  C:179CH         LINE#         44
  C:179EH         LINE#         45
  C:17A7H         LINE#         46
  C:17A7H         LINE#         47
  C:17A8H         LINE#         48
  C:17ADH         LINE#         49
  C:17AFH         LINE#         50
  C:17B8H         LINE#         51
  C:17BAH         LINE#         52
  -------         ENDPROC       DS18B20_CHECK
  -------         PROC          DS18B20_READ_BIT
  -------         DO            
  D:0004H         SYMBOL        dat
  -------         ENDDO         
  C:191DH         LINE#         60
  C:191DH         LINE#         61
  C:191DH         LINE#         62
  C:191FH         LINE#         64
  C:1921H         LINE#         65
  C:1923H         LINE#         66
  C:1925H         LINE#         67
  C:1927H         LINE#         68
  C:192DH         LINE#         69
  C:192FH         LINE#         70
  C:1934H         LINE#         71
  C:1936H         LINE#         72
  -------         ENDPROC       DS18B20_READ_BIT
  -------         PROC          DS18B20_READ_BYTE
  -------         DO            
  D:0003H         SYMBOL        i
  D:0002H         SYMBOL        dat
  D:0007H         SYMBOL        temp
  -------         ENDDO         
  C:18E7H         LINE#         80
  C:18E7H         LINE#         81
  C:18E7H         LINE#         82
  C:18E9H         LINE#         83
  C:18EAH         LINE#         84
  C:18EBH         LINE#         86
  C:18EBH         LINE#         87
  C:18EBH         LINE#         88
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  10:34:31  PAGE 12


  C:18EEH         LINE#         89
  C:18FBH         LINE#         90
  C:18FFH         LINE#         91
  C:1901H         LINE#         92
  -------         ENDPROC       DS18B20_READ_BYTE
  -------         PROC          L?0035
  -------         ENDPROC       L?0035
  -------         PROC          _DS18B20_WRITE_BYTE
  D:0004H         SYMBOL        dat
  -------         DO            
  D:0003H         SYMBOL        i
  D:0002H         SYMBOL        temp
  -------         ENDDO         
  C:17F8H         LINE#         100
  C:17FAH         LINE#         101
  C:17FAH         LINE#         102
  C:17FCH         LINE#         103
  C:17FCH         LINE#         105
  C:17FCH         LINE#         106
  C:17FCH         LINE#         107
  C:1800H         LINE#         108
  C:1804H         LINE#         109
  C:1807H         LINE#         110
  C:1807H         LINE#         111
  C:1809H         LINE#         112
  C:180BH         LINE#         113
  C:180DH         LINE#         114
  C:1812H         LINE#         115
  C:1814H         LINE#         117
  C:1814H         LINE#         118
  C:1816H         LINE#         119
  C:181BH         LINE#         120
  C:181DH         LINE#         121
  C:181FH         LINE#         122
  C:181FH         LINE#         123
  C:1823H         LINE#         124
  -------         ENDPROC       _DS18B20_WRITE_BYTE
  -------         PROC          DS18B20_START
  C:0003H         LINE#         132
  C:0003H         LINE#         133
  C:0003H         LINE#         134
  C:0003H         LINE#         135
  C:0003H         LINE#         136
  C:0006H         LINE#         137
  -------         ENDPROC       DS18B20_START
  -------         PROC          DS18B20_INIT
  C:19D9H         LINE#         146
  C:19D9H         LINE#         147
  C:19D9H         LINE#         148
  C:19DCH         LINE#         149
  C:19DFH         LINE#         150
  -------         ENDPROC       DS18B20_INIT
  -------         PROC          DS18B20_READ_TEMPERTURE
  -------         DO            
  D:0008H         SYMBOL        temp
  D:0007H         SYMBOL        dath
  D:0001H         SYMBOL        datl
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  10:34:31  PAGE 13


  D:000CH         SYMBOL        value
  -------         ENDDO         
  C:160AH         LINE#         158
  C:160AH         LINE#         159
  C:160AH         LINE#         161
  C:160CH         LINE#         162
  C:160DH         LINE#         163
  C:1611H         LINE#         165
  C:1614H         LINE#         166
  C:1614H         LINE#         167
  C:1614H         LINE#         168
  C:1617H         LINE#         169
  C:161CH         LINE#         171
  C:1621H         LINE#         172
  C:1624H         LINE#         173
  C:1630H         LINE#         175
  C:1637H         LINE#         176
  C:1637H         LINE#         177
  C:1648H         LINE#         178
  C:1656H         LINE#         179
  C:1658H         LINE#         181
  C:1658H         LINE#         182
  C:1672H         LINE#         183
  C:1672H         LINE#         184
  C:167AH         LINE#         185
  -------         ENDPROC       DS18B20_READ_TEMPERTURE
  -------         ENDMOD        DS18B20

  -------         MODULE        LCD9648
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0080H         PUBLIC        P0
  D:0090H         PUBLIC        P1
  D:00A0H         PUBLIC        P2
  D:00B0H         PUBLIC        P3
  C:1913H         PUBLIC        _WriteData
  C:14F0H         PUBLIC        _LCD9648_Write16EnCHAR1
  D:00A8H         PUBLIC        IE
  D:00B8H         PUBLIC        IP
  C:1997H         PUBLIC        _WriteComm
  B:00A0H.7       PUBLIC        RS
  C:167BH         PUBLIC        LCD9648_Init
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  B:0080H.0       PUBLIC        CS0
  C:000EH         PUBLIC        CN16CHARfont
  C:0250H         PUBLIC        EN16CHARfont
  B:00A0H.5       PUBLIC        SDA
  C:11A8H         PUBLIC        _LCD9648_Write16CnCHAR
  C:131EH         PUBLIC        _LCD9648_Write16EnCHAR
  B:00A0H.6       PUBLIC        SCL
  D:00C8H         PUBLIC        T2CON
  C:1937H         PUBLIC        _SendDataSPI
  B:0080H.1       PUBLIC        RST
  C:1857H         PUBLIC        LCD9648_Clear
  D:00D0H         PUBLIC        PSW
  -------         PROC          _SENDDATASPI
  D:0007H         SYMBOL        dat
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  10:34:31  PAGE 14


  -------         DO            
  D:0006H         SYMBOL        i
  -------         ENDDO         
  C:1937H         LINE#         4
  C:1937H         LINE#         5
  C:1937H         LINE#         8
  C:1939H         LINE#         9
  C:1939H         LINE#         10
  C:1941H         LINE#         11
  C:1943H         LINE#         13
  C:1947H         LINE#         15
  C:1949H         LINE#         16
  C:194BH         LINE#         17
  C:194FH         LINE#         18
  -------         ENDPROC       _SENDDATASPI
  C:1991H         SYMBOL        L?0067
  C:1993H         SYMBOL        L?0068
  -------         PROC          L?0066
  -------         ENDPROC       L?0066
  C:1991H         SYMBOL        L?0067
  C:1993H         SYMBOL        L?0068
  -------         PROC          _WRITECOMM
  D:0006H         SYMBOL        i
  C:1997H         LINE#         20
  C:1997H         LINE#         21
  C:1997H         LINE#         23
  C:1999H         LINE#         24
  C:199BH         LINE#         26
  C:199EH         LINE#         28
  C:19A0H         LINE#         30
  -------         ENDPROC       _WRITECOMM
  C:1907H         SYMBOL        L?0070
  -------         PROC          L?0069
  -------         ENDPROC       L?0069
  C:1907H         SYMBOL        L?0070
  -------         PROC          _WRITEDATA
  D:0006H         SYMBOL        i
  C:1913H         LINE#         31
  C:1913H         LINE#         32
  C:1913H         LINE#         33
  C:1915H         LINE#         34
  C:1917H         LINE#         36
  C:191AH         LINE#         38
  C:191CH         LINE#         39
  -------         ENDPROC       _WRITEDATA
  -------         PROC          LCD9648_INIT
  -------         DO            
  D:0006H         SYMBOL        i
  -------         ENDDO         
  C:167BH         LINE#         41
  C:167BH         LINE#         42
  C:167BH         LINE#         46
  C:167DH         LINE#         47
  C:168BH         LINE#         49
  C:168DH         LINE#         50
  C:169BH         LINE#         52
  C:169DH         LINE#         53
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  10:34:31  PAGE 15


  C:16ABH         LINE#         55
  C:16B2H         LINE#         56
  C:16B9H         LINE#         57
  C:16C0H         LINE#         58
  C:16C7H         LINE#         59
  C:16CEH         LINE#         60
  C:16D5H         LINE#         61
  C:16DCH         LINE#         62
  -------         ENDPROC       LCD9648_INIT
  -------         PROC          LCD9648_CLEAR
  -------         DO            
  D:0005H         SYMBOL        i
  D:0004H         SYMBOL        j
  -------         ENDDO         
  C:1857H         LINE#         67
  C:1857H         LINE#         68
  C:1857H         LINE#         71
  C:1859H         LINE#         72
  C:1859H         LINE#         73
  C:1860H         LINE#         74
  C:1866H         LINE#         75
  C:186DH         LINE#         76
  C:1873H         LINE#         78
  C:1875H         LINE#         79
  C:1875H         LINE#         80
  C:187BH         LINE#         81
  C:187FH         LINE#         82
  C:1883H         LINE#         83
  -------         ENDPROC       LCD9648_CLEAR
  -------         PROC          _LCD9648_WRITE16CNCHAR
  D:0061H         SYMBOL        x
  D:0062H         SYMBOL        y
  D:0001H         SYMBOL        cn
  -------         DO            
  D:0004H         SYMBOL        j
  D:0063H         SYMBOL        x1
  D:0064H         SYMBOL        x2
  D:0005H         SYMBOL        wordNum
  -------         ENDDO         
  C:11A8H         LINE#         86
  C:11ACH         LINE#         87
  C:11ACH         LINE#         91
  C:11B3H         LINE#         92
  C:11B3H         LINE#         93
  C:11B6H         LINE#         94
  C:11B6H         LINE#         97
  C:11BDH         LINE#         98
  C:11BDH         LINE#         99
  C:11C0H         LINE#         100
  C:11C0H         LINE#         101
  C:11C6H         LINE#         103
  C:11CCH         LINE#         104
  C:11D4H         LINE#         105
  C:11D4H         LINE#         108
  C:11DBH         LINE#         110
  C:11E2H         LINE#         111
  C:11E8H         LINE#         113
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  10:34:31  PAGE 16


  C:11EBH         LINE#         114
  C:11F2H         LINE#         115
  C:11F4H         LINE#         116
  C:11F4H         LINE#         118
  C:1227H         LINE#         120
  C:1227H         LINE#         121
  C:1228H         LINE#         122
  C:1228H         LINE#         123
  C:122DH         LINE#         124
  C:122DH         LINE#         126
  C:1234H         LINE#         129
  C:1237H         LINE#         130
  C:123EH         LINE#         131
  C:123EH         LINE#         132
  C:124EH         LINE#         133
  C:1252H         LINE#         134
  C:1258H         LINE#         135
  C:1258H         LINE#         136
  C:125EH         LINE#         137
  C:1265H         LINE#         139
  C:1268H         LINE#         140
  C:126AH         LINE#         141
  -------         ENDPROC       _LCD9648_WRITE16CNCHAR
  -------         PROC          _LCD9648_WRITE16ENCHAR
  D:0073H         SYMBOL        x
  D:0074H         SYMBOL        y
  D:0001H         SYMBOL        en
  -------         DO            
  D:0004H         SYMBOL        j
  D:0075H         SYMBOL        x1
  D:0076H         SYMBOL        x2
  D:0005H         SYMBOL        wordNum
  -------         ENDDO         
  C:131EH         LINE#         143
  C:1322H         LINE#         144
  C:1322H         LINE#         148
  C:1329H         LINE#         149
  C:1329H         LINE#         150
  C:132CH         LINE#         151
  C:132CH         LINE#         154
  C:1333H         LINE#         155
  C:1333H         LINE#         156
  C:1336H         LINE#         157
  C:1336H         LINE#         158
  C:133CH         LINE#         160
  C:1342H         LINE#         161
  C:134AH         LINE#         162
  C:134AH         LINE#         165
  C:1351H         LINE#         167
  C:1358H         LINE#         168
  C:135EH         LINE#         170
  C:1363H         LINE#         171
  C:136AH         LINE#         172
  C:136CH         LINE#         173
  C:136CH         LINE#         175
  C:1384H         LINE#         176
  C:1384H         LINE#         177
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  10:34:31  PAGE 17


  C:1385H         LINE#         178
  C:1385H         LINE#         179
  C:138AH         LINE#         180
  C:138AH         LINE#         182
  C:1391H         LINE#         185
  C:1396H         LINE#         186
  C:139DH         LINE#         187
  C:139DH         LINE#         188
  C:13ADH         LINE#         189
  C:13B1H         LINE#         190
  C:13B7H         LINE#         191
  C:13B7H         LINE#         192
  C:13BDH         LINE#         193
  C:13C4H         LINE#         195
  C:13C7H         LINE#         196
  C:13C9H         LINE#         197
  -------         ENDPROC       _LCD9648_WRITE16ENCHAR
  -------         PROC          _LCD9648_WRITE16ENCHAR1
  D:0001H         SYMBOL        x
  D:0005H         SYMBOL        y
  D:0003H         SYMBOL        en
  -------         DO            
  D:0002H         SYMBOL        j
  D:0014H         SYMBOL        x1
  D:0015H         SYMBOL        x2
  D:0004H         SYMBOL        wordNum
  -------         ENDDO         
  C:14F0H         LINE#         198
  C:14F2H         LINE#         199
  C:14F2H         LINE#         203
  C:14F8H         LINE#         204
  C:14F8H         LINE#         205
  C:14FBH         LINE#         206
  C:14FBH         LINE#         209
  C:1501H         LINE#         210
  C:1501H         LINE#         211
  C:1504H         LINE#         212
  C:1504H         LINE#         213
  C:1508H         LINE#         215
  C:150EH         LINE#         217
  C:150EH         LINE#         220
  C:1515H         LINE#         222
  C:151BH         LINE#         223
  C:1520H         LINE#         225
  C:1525H         LINE#         226
  C:152CH         LINE#         227
  C:152EH         LINE#         228
  C:152EH         LINE#         230
  C:1542H         LINE#         231
  C:1542H         LINE#         232
  C:1543H         LINE#         233
  C:1543H         LINE#         234
  C:1548H         LINE#         235
  C:1548H         LINE#         237
  C:154EH         LINE#         240
  C:1553H         LINE#         241
  C:155AH         LINE#         242
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  10:34:31  PAGE 18


  C:155AH         LINE#         243
  C:156FH         LINE#         244
  C:1573H         LINE#         245
  C:1577H         LINE#         246
  C:1577H         LINE#         247
  C:157BH         LINE#         248
  C:157BH         LINE#         250
  C:157BH         LINE#         251
  C:157DH         LINE#         252
  -------         ENDPROC       _LCD9648_WRITE16ENCHAR1
  -------         ENDMOD        LCD9648

  -------         MODULE        KEY
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  C:1950H         PUBLIC        Key_Read
  D:00A8H         PUBLIC        IE
  D:00B8H         PUBLIC        IP
  B:0090H.0       PUBLIC        k1
  B:0090H.1       PUBLIC        k2
  B:0090H.2       PUBLIC        k3
  B:0090H.3       PUBLIC        k4
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  D:00C8H         PUBLIC        T2CON
  D:00D0H         PUBLIC        PSW
  -------         PROC          KEY_READ
  -------         DO            
  D:0007H         SYMBOL        temp
  -------         ENDDO         
  C:1950H         LINE#         4
  C:1950H         LINE#         5
  C:1950H         LINE#         6
  C:1952H         LINE#         8
  C:1956H         LINE#         9
  C:195BH         LINE#         10
  C:1960H         LINE#         11
  C:1965H         LINE#         12
  C:1965H         LINE#         13
  -------         ENDPROC       KEY_READ
  -------         ENDMOD        KEY

  -------         MODULE        RTC3085
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  D:00A8H         PUBLIC        IE
  C:17BBH         PUBLIC        _Ds1302Write
  C:1966H         PUBLIC        Ds1302ReadTime
  C:1884H         PUBLIC        Ds1302Init
  D:00B8H         PUBLIC        IP
  B:00B0H.6       PUBLIC        SCLK
  B:00B0H.4       PUBLIC        DSIO
  D:000EH         PUBLIC        TIME
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  10:34:31  PAGE 19


  C:19D3H         PUBLIC        WRITE_RTC_ADDR
  C:19CDH         PUBLIC        READ_RTC_ADDR
  D:00C8H         PUBLIC        T2CON
  B:00B0H.5       PUBLIC        RST
  D:00D0H         PUBLIC        PSW
  C:1736H         PUBLIC        _Ds1302Read
  -------         PROC          _DS1302WRITE
  D:0007H         SYMBOL        addr
  D:0005H         SYMBOL        dat
  -------         DO            
  D:0006H         SYMBOL        n
  -------         ENDDO         
  C:17BBH         LINE#         25
  C:17BBH         LINE#         26
  C:17BBH         LINE#         28
  C:17BDH         LINE#         29
  C:17BEH         LINE#         31
  C:17C0H         LINE#         32
  C:17C1H         LINE#         33
  C:17C3H         LINE#         34
  C:17C4H         LINE#         36
  C:17C6H         LINE#         37
  C:17C6H         LINE#         38
  C:17CAH         LINE#         39
  C:17CEH         LINE#         40
  C:17D0H         LINE#         41
  C:17D1H         LINE#         42
  C:17D3H         LINE#         43
  C:17D4H         LINE#         44
  C:17D8H         LINE#         45
  C:17DAH         LINE#         46
  C:17DAH         LINE#         47
  C:17DEH         LINE#         48
  C:17E2H         LINE#         49
  C:17E4H         LINE#         50
  C:17E5H         LINE#         51
  C:17E7H         LINE#         52
  C:17E8H         LINE#         53
  C:17ECH         LINE#         55
  C:17EEH         LINE#         56
  C:17EFH         LINE#         57
  -------         ENDPROC       _DS1302WRITE
  -------         PROC          _DS1302READ
  D:0007H         SYMBOL        addr
  -------         DO            
  D:0005H         SYMBOL        n
  D:0006H         SYMBOL        dat
  D:0007H         SYMBOL        dat1
  -------         ENDDO         
  C:1736H         LINE#         66
  C:1736H         LINE#         67
  C:1736H         LINE#         69
  C:1738H         LINE#         70
  C:1739H         LINE#         72
  C:173BH         LINE#         73
  C:173CH         LINE#         74
  C:173EH         LINE#         75
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  10:34:31  PAGE 20


  C:173FH         LINE#         77
  C:1741H         LINE#         78
  C:1741H         LINE#         79
  C:1745H         LINE#         80
  C:1749H         LINE#         81
  C:174BH         LINE#         82
  C:174CH         LINE#         83
  C:174EH         LINE#         84
  C:174FH         LINE#         85
  C:1753H         LINE#         86
  C:1754H         LINE#         87
  C:1756H         LINE#         88
  C:1756H         LINE#         89
  C:175BH         LINE#         90
  C:1767H         LINE#         91
  C:1769H         LINE#         92
  C:176AH         LINE#         93
  C:176CH         LINE#         94
  C:176DH         LINE#         95
  C:1771H         LINE#         97
  C:1773H         LINE#         98
  C:1774H         LINE#         99
  C:1776H         LINE#         100
  C:1777H         LINE#         101
  C:1779H         LINE#         102
  C:177AH         LINE#         103
  C:177CH         LINE#         104
  C:177DH         LINE#         105
  C:177FH         LINE#         106
  -------         ENDPROC       _DS1302READ
  -------         PROC          DS1302INIT
  -------         DO            
  D:0004H         SYMBOL        n
  -------         ENDDO         
  C:1884H         LINE#         115
  C:1884H         LINE#         116
  C:1884H         LINE#         118
  C:188BH         LINE#         119
  C:188DH         LINE#         120
  C:188DH         LINE#         121
  C:189CH         LINE#         122
  C:18A0H         LINE#         123
  -------         ENDPROC       DS1302INIT
  -------         PROC          DS1302READTIME
  -------         DO            
  D:0003H         SYMBOL        n
  -------         ENDDO         
  C:1966H         LINE#         133
  C:1966H         LINE#         134
  C:1966H         LINE#         136
  C:1968H         LINE#         137
  C:1968H         LINE#         138
  C:1977H         LINE#         139
  C:197BH         LINE#         141
  -------         ENDPROC       DS1302READTIME
  -------         ENDMOD        RTC3085

BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  10:34:31  PAGE 21


  -------         MODULE        ?C?FPADD
  C:08A6H         PUBLIC        ?C?FPADD
  C:08A2H         PUBLIC        ?C?FPSUB
  -------         ENDMOD        ?C?FPADD

  -------         MODULE        ?C?FPMUL
  C:0997H         PUBLIC        ?C?FPMUL
  -------         ENDMOD        ?C?FPMUL

  -------         MODULE        ?C?FPCMP
  C:0A9FH         PUBLIC        ?C?FPCMP
  C:0A9DH         PUBLIC        ?C?FPCMP3
  -------         ENDMOD        ?C?FPCMP

  -------         MODULE        ?C?FCAST
  C:0B20H         PUBLIC        ?C?FCASTC
  C:0B1BH         PUBLIC        ?C?FCASTI
  C:0B16H         PUBLIC        ?C?FCASTL
  -------         ENDMOD        ?C?FCAST

  -------         MODULE        ?C?CASTF
  C:0B54H         PUBLIC        ?C?CASTF
  -------         ENDMOD        ?C?CASTF

  -------         MODULE        ?C?CLDPTR
  C:0BD5H         PUBLIC        ?C?CLDPTR
  -------         ENDMOD        ?C?CLDPTR

  -------         MODULE        ?C?CLDOPTR
  C:0BEEH         PUBLIC        ?C?CLDOPTR
  -------         ENDMOD        ?C?CLDOPTR

  -------         MODULE        ?C?CSTPTR
  C:0C1BH         PUBLIC        ?C?CSTPTR
  -------         ENDMOD        ?C?CSTPTR

  -------         MODULE        ?C?CSTOPTR
  C:0C2DH         PUBLIC        ?C?CSTOPTR
  -------         ENDMOD        ?C?CSTOPTR

  -------         MODULE        ?C?LNEG
  C:0C4FH         PUBLIC        ?C?LNEG
  -------         ENDMOD        ?C?LNEG

  -------         MODULE        ?C?LLDIDATA
  C:0C5DH         PUBLIC        ?C?LLDIDATA
  -------         ENDMOD        ?C?LLDIDATA

  -------         MODULE        ?C?LSTIDATA
  C:0C69H         PUBLIC        ?C?LSTIDATA
  -------         ENDMOD        ?C?LSTIDATA

  -------         MODULE        ?C?LSTKIDATA
  C:0C75H         PUBLIC        ?C?LSTKIDATA
  -------         ENDMOD        ?C?LSTKIDATA

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
BL51 BANKED LINKER/LOCATER V6.22                                                      07/08/2025  10:34:31  PAGE 22


    SEGMENT: ?PR?DS18B20_INIT?DS18B20

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?DS18B20_READ_TEMPERTURE?DS18B20

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?_LCD9648_WRITE16ENCHAR1?LCD9648

Program Size: data=114.1 xdata=0 code=6624
LINK/LOCATE RUN COMPLETE.  3 WARNING(S),  0 ERROR(S)
