#include <reg52.h>
//#include <adc3085.h>
#include <lcd9648.h>
//#include <uart3085.h>
#include <key.h>
#include "rtc3085.h"
#include "ds18b20.h"

sbit Buzzer = P1^5;  // ���������Ŷ���

unsigned int Timer0_count = 0;   // ��ʱ��������
unsigned char Time_Flag = 0;     // 6�뵽���־
unsigned char Time_Flag_Count;   // ��ʱ���ʹ�����������
unsigned char LCD_Init_Mode = 1; //ģʽ1����ʾ����ѧ�Ű༶ ģʽ2����ʾ���ܽ��� ģʽ3��������ʾ
bit LCD_Init_End_Flag = 0; //��ʼ��ʾ������־λ

unsigned char Key_Slow_Down = 0;
unsigned char Key_Val,Key_Up,Key_Down,Key_Old;

unsigned char LCD_Page_Mode = 0; // 0=��ʾҳ1, 1=��ʾҳ2, 2=����ҳ1, 3=����ҳ2
float Temperature = 0.0;  // ʵʱ�¶ȱ���
unsigned char State_Index = 0;//״̬��ʾ��������ָ��
float State_Disp[4] = {30.0, 10.0, 10.0, 10.0};     //LCD��ʾ������ĸ�������
           // NTC��Դ�����¶�/DS18B20���±������¶�/�����ֵ��ѹ/��ֵ�������
unsigned char Display_Buffer[16]; // ��ʾ��������������˸
unsigned char Last_LCD_Mode = 0xFF; // �ϴ�LCDģʽ�����ڼ��ģʽ�л�
unsigned char Last_State_Index = 0xFF; // �ϴ�״̬���������ڼ��ѡ�����л�

// ҳ����Ʊ���
unsigned char Display_Page = 0;    // ��ʾ����ҳ�棺0=��һҳ��1=�ڶ�ҳ
unsigned char Setting_Page = 0;    // ���ý���ҳ�棺0=��һҳ��1=�ڶ�ҳ

// ���������ò������ڶ�ҳ�ã�
float Setting_Disp[3] = {12.0, 5.0, 24.0}; // �����ѹ/�������/������ѹ
unsigned char Setting_Index = 0;   // ���ý���ڶ�ҳ�Ĳ�������

// BCDתʮ���ƺ���
unsigned char BCD_To_Dec(unsigned char bcd) { return ((bcd>>4)*10 + (bcd&0x0F)); }
unsigned char Last_Display_Page = 0xFF; // �ϴ���ʾҳ��
unsigned char Last_Setting_Page = 0xFF; // �ϴ�����ҳ��

unsigned char Last_LCD_Page_Mode = 0xFF; // �ϴ�ҳ��ģʽ�����ڼ��ҳ��仯

// ����ʱ��ת�ַ�������
void Time_To_String(unsigned char *time_str)
{
    Ds1302ReadTime(); // ��ȡ��ǰʱ��
    
    // ֱ��ʹ��BCD��ת��������Ҫ�м����
    time_str[0] = ((TIME[3] >> 4) & 0x0F) + '0';  // ʱʮλ
    time_str[1] = (TIME[3] & 0x0F) + '0';         // ʱ��λ
    time_str[2] = ':';
    time_str[3] = ((TIME[4] >> 4) & 0x0F) + '0';  // ��ʮλ
    time_str[4] = (TIME[4] & 0x0F) + '0';         // �ָ�λ
    time_str[5] = ':';
    time_str[6] = ((TIME[5] >> 4) & 0x0F) + '0';  // ��ʮλ
    time_str[7] = (TIME[5] & 0x0F) + '0';         // ���λ
    time_str[8] = '\0';
}
/*��������*/
void Key_Proc()
{
    if(Key_Slow_Down) return;
    Key_Slow_Down = 1;
    
    Key_Val = Key_Read();
    Key_Down = Key_Val & (Key_Old ^ Key_Val);
    Key_Up = ~Key_Val & (Key_Old ^ Key_Val);	
    Key_Old = Key_Val;
    
    switch(Key_Down)
    {
        case 1: // ����
            if(LCD_Page_Mode == 2) // ����ҳ1
            {
                if(State_Index == 0 || State_Index == 1) // �¶Ȳ�������1��C
                {
                    State_Disp[State_Index] += 1.0;
                    if(State_Index == 0 && State_Disp[State_Index] > 85.0) State_Disp[State_Index] = 85.0;
                    if(State_Index == 1 && State_Disp[State_Index] > 90.0) State_Disp[State_Index] = 90.0;
                }
                else if(State_Index == 2) // ��ѹ��������0.1
                {
                    State_Disp[State_Index] += 0.1;
                    if(State_Disp[State_Index] > 85.0) State_Disp[State_Index] = 85.0;
                }
            }
            else if(LCD_Page_Mode == 3) // ����ҳ2
            {
                Setting_Disp[State_Index] += 0.1;
                if(Setting_Disp[State_Index] > 99.9) Setting_Disp[State_Index] = 99.9;
            }
            break;
            
        case 2: // ����
            if(LCD_Page_Mode == 2) // ����ҳ1
            {
                if(State_Index == 0 || State_Index == 1) // �¶Ȳ�������1��C
                {
                    State_Disp[State_Index] -= 1.0;
                    if(State_Index == 0 && State_Disp[State_Index] < 25.0) State_Disp[State_Index] = 25.0;
                    if(State_Index == 1 && State_Disp[State_Index] < 20.0) State_Disp[State_Index] = 20.0;
                }
                else if(State_Index == 2) // ��ѹ��������0.1
                {
                    State_Disp[State_Index] -= 0.1;
                    if(State_Disp[State_Index] < 0.0) State_Disp[State_Index] = 0.0;
                }
            }
            else if(LCD_Page_Mode == 3) // ����ҳ2
            {
                Setting_Disp[State_Index] -= 0.1;
                if(Setting_Disp[State_Index] < 0.0) Setting_Disp[State_Index] = 0.0;
            }
            break;
            
        case 3: // ҳ���л���0->1->2->3->0ѭ��
            if(++LCD_Page_Mode == 4) LCD_Page_Mode = 0;
            State_Index = 0; // �л�ҳ��ʱ���ò���ָ��
            break;
            
        case 4: // ����ָ���л�����������ҳ����Ч��
            if(LCD_Page_Mode == 2 || LCD_Page_Mode == 3) // ֻ������ҳ����Ч
            {
                if(++State_Index == 3) State_Index = 0; // ÿҳ3������
            }
            break;
    }
}


// ������ת�ַ���������һλС����
void Float_To_String(float value, unsigned char *str, unsigned char unit)
{
    unsigned char integer_part = (unsigned char)value;
    unsigned char decimal_part = (unsigned char)((value - integer_part) * 10);
    
    str[0] = integer_part / 10 + '0';
    str[1] = integer_part % 10 + '0';
    str[2] = '.';
    str[3] = decimal_part + '0';
    str[4] = unit; // 'C', 'V', 'A'
    str[5] = '\0';
}

/*-----------------------------------------------------------------------------
 * ��������: Timer0_Init
 * ��������: ��ʱ��0��ʼ����1ms�ж�һ��
 *----------------------------------------------------------------------------*/
void Timer0_Init(void)
{
    TMOD &= 0xF0;    // �����ʱ��0ģʽλ
    TMOD |= 0x01;    // ��ʱ��0������ģʽ1��16λ��ʱ��
    TH0 = 0xFC;      // 1ms@11.0592MHz
    TL0 = 0x66;
    ET0 = 1;         // ʹ�ܶ�ʱ��0�ж�
    EA = 1;          // ʹ�����ж�
    TR0 = 1;         // ������ʱ��0
}

/*-----------------------------------------------------------------------------
 * ��������: Timer0_ISR
 * ��������: ��ʱ��0�жϷ�����
 *----------------------------------------------------------------------------*/
void Timer0_ISR(void) interrupt 1
{
    TH0 = 0xFC;      // ��װ��ֵ
    TL0 = 0x66;
    
    Timer0_count++;   // ����������
    
    if(Timer0_count >= 6000)  // 6000ms = 6��
    {
        Time_Flag = 1;       // ����6�뵽���־
        Timer0_count = 0;     // ���ü�����
    }
    
    if(++Key_Slow_Down == 20) Key_Slow_Down = 0;
}

void LCD9648_Init_Proc()
{
	switch(LCD_Init_Mode)
	{
		case 1:
			LCD9648_Init();
			LCD9648_Clear();
			
			LCD9648_Write16CnCHAR(0,0,"��");
			LCD9648_Write16CnCHAR(16,0,"��");
			LCD9648_Write16CnCHAR(32,0,"��");
		
			LCD9648_Write16EnCHAR(0,2,"202303103085");
			
			LCD9648_Write16CnCHAR(0,4,"��");
			LCD9648_Write16CnCHAR(16,4,"��");
			LCD9648_Write16EnCHAR(32,4,"23");
			LCD9648_Write16EnCHAR(48,4,"4");
			LCD9648_Write16CnCHAR(56,4,"��");
		break;
		
		case 2:
			LCD9648_Init();
			LCD9648_Clear();
			
			LCD9648_Write16CnCHAR(0,0,"��");
			LCD9648_Write16CnCHAR(16,0,"Я");
			LCD9648_Write16CnCHAR(32,0,"ʽ");
			LCD9648_Write16CnCHAR(48,0,"��");
			LCD9648_Write16CnCHAR(64,0,"��");
			LCD9648_Write16CnCHAR(80,0,"��");
			
			LCD9648_Write16EnCHAR(0,2,"25");//x=0,y=4,���ַ�
			LCD9648_Write16CnCHAR(16,2,"��");
			LCD9648_Write16EnCHAR(32,2,"07");
			LCD9648_Write16CnCHAR(48,2,"��");
			LCD9648_Write16EnCHAR(64,2,"11");
			LCD9648_Write16CnCHAR(80,2,"��");
		
			LCD9648_Write16CnCHAR(0,4,"��");
			LCD9648_Write16CnCHAR(16,4,"��");
			LCD9648_Write16EnCHAR(32,4,"1.");
			LCD9648_Write16EnCHAR(48,4,"23");
		break;
		
		case 3: //������ʾģʽ
            LCD9648_Init();
            LCD9648_Clear();
        break;
		
		default:
			
		break;
	}	
}

void LCD_Init_Test() //��ʼ״̬ʱ����⺯��
{
	if(Time_Flag)     //6��ʱ�䵽
	{
		Time_Flag = 0;
		Time_Flag_Count += 1;
		LCD_Init_Mode++; //�л�����һ��ģʽ
		if(LCD_Init_Mode <= 3)
		{
			LCD9648_Init_Proc(); //������ʾ
		}
		if(Time_Flag_Count == 2)
			LCD_Init_End_Flag ^= 1;
	}
	
}

// �¶ȶ�ȡ�ʹ�������
void Temperature_Proc()
{
    static unsigned char temp_count = 0;
    
    // ÿ100ms��ȡһ���¶ȣ����Ͷ�ȡƵ�ʣ�
    if(++temp_count >= 100)
    {
        temp_count = 0;
        Temperature = ds18b20_read_temperture(); // ��ȡDS18B20�¶�
        
        // �¶ȱ������
        if(Temperature > State_Disp[1]) // �����ý���ڶ��������Ƚ�
        {
            Buzzer = 0; // �������죨�͵�ƽ��Ч��
        }
        else
        {
            Buzzer = 1; // ������ֹͣ
        }
    }
}

void LCD_Proc()
{
    unsigned char temp_str[16];
    unsigned char i;
    unsigned char need_clear = 0;
    unsigned char need_update_pointer = 0;
    
    if(!LCD_Init_End_Flag) return; // ��ʼ��δ���ʱ����ʾ
    
    // ����Ƿ���Ҫ������ֻ��ҳ���л�ʱ��������
    if(Last_LCD_Page_Mode != LCD_Page_Mode)
    {
        need_clear = 1;
        Last_LCD_Page_Mode = LCD_Page_Mode;
        LCD9648_Clear();
    }
    
    // ����Ƿ���Ҫ����ָ��
    if(Last_State_Index != State_Index)
    {
        need_update_pointer = 1;
        Last_State_Index = State_Index;
    }
    
    switch(LCD_Page_Mode)
    {
        case 0: // ��ʾҳ1 - ��ʾ����ֵ
			if(need_clear)
			{
				LCD9648_Write16EnCHAR(60,0,"[V1/2]");
				LCD9648_Write16EnCHAR(0,0,"NTC:");
				LCD9648_Write16EnCHAR(0,2,"SET:");  
				LCD9648_Write16EnCHAR(0,4,"VOL:");
			}
			
			// ʵʱ���²���ֵ
			Float_To_String(State_Disp[0], temp_str, 'C');
			LCD9648_Write16EnCHAR(32,0,temp_str);
			
			// ��ʾDS18B20ʵʱ�¶ȣ����ԭ����State_Disp[1]��
			Float_To_String(Temperature, temp_str, 'C');
			LCD9648_Write16EnCHAR(32,2,temp_str);
			
			Float_To_String(State_Disp[2], temp_str, 'V');
			LCD9648_Write16EnCHAR(32,4,temp_str);
			break;
            
	    case 1: // ��ʾҳ2 - ��ʾʱ��
			if(need_clear)
			{
				LCD9648_Write16EnCHAR(80,0,"V2");
				LCD9648_Write16EnCHAR(0,2,"TIM:"); // ��ΪTIM�Խ�ʡ�ռ�
			}
			
			// ʵʱ����ʱ��
			Time_To_String(temp_str);
			LCD9648_Write16EnCHAR(32,2,temp_str); // ����λ��ȷ��������ʾ
			break;
            
        case 2: // ����ҳ1 - ����ǰ3������
			if(need_clear)
			{
				LCD9648_Write16EnCHAR(60,0,"[S1/2]");
				LCD9648_Write16EnCHAR(8,0,"NTC:");
				LCD9648_Write16EnCHAR(8,2,"ALM:"); // ��ΪALM��ʾ�����¶���ֵ
				LCD9648_Write16EnCHAR(8,4,"VOL:");
			}
			
			// ����ָ��
			if(need_clear || need_update_pointer)
			{
				for(i = 0; i < 3; i++)
				{
					if(i == State_Index)
						LCD9648_Write16EnCHAR(0, i*2, ">");
					else
						LCD9648_Write16EnCHAR(0, i*2, " ");
				}
			}
			
			// ʵʱ���²���ֵ
			Float_To_String(State_Disp[0], temp_str, 'C');
			LCD9648_Write16EnCHAR(40,0,temp_str);
			
			Float_To_String(State_Disp[1], temp_str, 'C'); // ������ֵ
			LCD9648_Write16EnCHAR(40,2,temp_str);
			
			Float_To_String(State_Disp[2], temp_str, 'V');
			LCD9648_Write16EnCHAR(40,4,temp_str);
			break;
            
        case 3: // ����ҳ2 - ���ú�3������
            if(need_clear)
            {
                LCD9648_Write16EnCHAR(80,0,"S2");
                LCD9648_Write16EnCHAR(8,0,"OUT:"); // �����ѹ
                LCD9648_Write16EnCHAR(8,2,"CUR:"); // �������
                LCD9648_Write16EnCHAR(8,4,"FBK:"); // ������ѹ
            }
            
            // ����ָ��
            if(need_clear || need_update_pointer)
            {
                for(i = 0; i < 3; i++)
                {
                    if(i == State_Index)
                        LCD9648_Write16EnCHAR(0, i*2, ">");
                    else
                        LCD9648_Write16EnCHAR(0, i*2, " ");
                }
            }
            
            // ʵʱ���²���ֵ
            Float_To_String(Setting_Disp[0], temp_str, 'V');
            LCD9648_Write16EnCHAR(40,0,temp_str);
            
            Float_To_String(Setting_Disp[1], temp_str, 'A');
            LCD9648_Write16EnCHAR(40,2,temp_str);
            
            Float_To_String(Setting_Disp[2], temp_str, 'V');
            LCD9648_Write16EnCHAR(40,4,temp_str);
            break;
    }
}

void main()
{
    // ��ʼ����ʾ��ر���
    Last_LCD_Page_Mode = 0xFF;
    Last_State_Index = 0xFF;
    
    // ��ʼ����������Ĭ�Ϲرգ�
    Buzzer = 1;
    
    // ��ʼ��DS18B20
    if(ds18b20_init() == 0)
    {
        // DS18B20��ʼ���ɹ�����ʼ��һ���¶�ת��
        ds18b20_start();
    }
    
    // ��ʼ��DS1302ʱ��
    Ds1302Init();
    
    LCD9648_Init_Proc(); // ��ʾ��ʼ��
    Timer0_Init();       // ������ʱ��
    
    while(1)
    {
        LCD_Init_Test();
        Key_Proc();
        LCD_Proc();
        Temperature_Proc(); // �����¶ȴ���
    }
}