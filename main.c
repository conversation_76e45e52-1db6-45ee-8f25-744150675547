#include <reg52.h>
//#include <adc3085.h>
#include <lcd9648.h>
//#include <uart3085.h>
#include <key.h>


unsigned int Timer0_count = 0;   // ��ʱ��������
unsigned char Time_Flag = 0;     // 6�뵽���־
unsigned char Time_Flag_Count;   // ��ʱ���ʹ�����������
unsigned char LCD_Init_Mode = 1; //ģʽ1����ʾ����ѧ�Ű༶ ģʽ2����ʾ���ܽ��� ģʽ3��������ʾ
bit LCD_Init_End_Flag = 0; //��ʼ��ʾ������־λ

unsigned char Key_Slow_Down = 0;
unsigned char Key_Val,Key_Up,Key_Down,Key_Old;

unsigned char LCD_Disp_Mode = 0; // 0��LCD��ʾ���桡����LCD���ý���
unsigned char Temperature;//ʵʱ�¶ȱ���
unsigned char State_Index = 0;//״̬��ʾ��������ָ��
float State_Disp[4] = {30.0, 10.0, 10.0, 10.0};     //LCD��ʾ������ĸ�������
           // NTC��Դ�����¶�/DS18B20���±������¶�/�����ֵ��ѹ/��ֵ�������
unsigned char Display_Buffer[16]; // ��ʾ��������������˸
unsigned char Last_LCD_Mode = 0xFF; // �ϴ�LCDģʽ�����ڼ��ģʽ�л�
unsigned char Last_State_Index = 0xFF; // �ϴ�״̬���������ڼ��ѡ�����л�


/*��������*/
void Key_Proc()
{
    if(Key_Slow_Down) return;
    Key_Slow_Down = 1;
    
    Key_Val = Key_Read();
    Key_Down = Key_Val & (Key_Old ^ Key_Val);
    Key_Up = ~Key_Val & (Key_Old ^ Key_Val);	
    Key_Old = Key_Val;
    
    switch(Key_Down)
    {
        case 1: // ����
            if(LCD_Disp_Mode == 1) // ֻ�����ý���������������
            {
                if(State_Index == 0 || State_Index == 1) // �¶Ȳ���������1��C
                {
                    State_Disp[State_Index] += 1.0;
                    if(State_Index == 0 && State_Disp[State_Index] > 85.0) State_Disp[State_Index] = 85.0; // NTC���85��C
                    if(State_Index == 1 && State_Disp[State_Index] > 90.0) State_Disp[State_Index] = 90.0; // DS18B20���90��C
                }
                else if(State_Index == 2 || State_Index == 3) // ��ѹ/��������������0.1
                {
                    State_Disp[State_Index] += 0.1;
                    if(State_Disp[State_Index] > 85.0) State_Disp[State_Index] = 85.0; // ���ֵ����
                }
            }
            break;
            
        case 2: // �Լ�
            if(LCD_Disp_Mode == 1) // ֻ�����ý���������������
            {
                if(State_Index == 0 || State_Index == 1) // �¶Ȳ���������1��C
                {
                    State_Disp[State_Index] -= 1.0;
                    if(State_Index == 0 && State_Disp[State_Index] < 25.0) State_Disp[State_Index] = 25.0; // NTC��С25��C
                    if(State_Index == 1 && State_Disp[State_Index] < 20.0) State_Disp[State_Index] = 20.0; // DS18B20��С20��C
                }
                else if(State_Index == 2 || State_Index == 3) // ��ѹ/��������������0.1
                {
                    State_Disp[State_Index] -= 0.1;
                    if(State_Disp[State_Index] < 0.0) State_Disp[State_Index] = 0.0; // ��Сֵ����
                }
            }
            break;
            
        case 3: // �����л�
            if(++LCD_Disp_Mode == 2) LCD_Disp_Mode = 0;
            break;
            
        case 4: // ����ָ������
            if(LCD_Disp_Mode == 1) // ֻ�����ý�������ѡ���л�
            {
                if(++State_Index == 4) State_Index = 0;
            }	
            break;
    }
}


// ������ת�ַ���������һλС����
void Float_To_String(float value, unsigned char *str, unsigned char unit)
{
    unsigned char integer_part = (unsigned char)value;
    unsigned char decimal_part = (unsigned char)((value - integer_part) * 10);
    
    str[0] = integer_part / 10 + '0';
    str[1] = integer_part % 10 + '0';
    str[2] = '.';
    str[3] = decimal_part + '0';
    str[4] = unit; // 'C', 'V', 'A'
    str[5] = '\0';
}

/*-----------------------------------------------------------------------------
 * ��������: Timer0_Init
 * ��������: ��ʱ��0��ʼ����1ms�ж�һ��
 *----------------------------------------------------------------------------*/
void Timer0_Init(void)
{
    TMOD &= 0xF0;    // �����ʱ��0ģʽλ
    TMOD |= 0x01;    // ��ʱ��0������ģʽ1��16λ��ʱ��
    TH0 = 0xFC;      // 1ms@11.0592MHz
    TL0 = 0x66;
    ET0 = 1;         // ʹ�ܶ�ʱ��0�ж�
    EA = 1;          // ʹ�����ж�
    TR0 = 1;         // ������ʱ��0
}

/*-----------------------------------------------------------------------------
 * ��������: Timer0_ISR
 * ��������: ��ʱ��0�жϷ�����
 *----------------------------------------------------------------------------*/
void Timer0_ISR(void) interrupt 1
{
    TH0 = 0xFC;      // ��װ��ֵ
    TL0 = 0x66;
    
    Timer0_count++;   // ����������
    
    if(Timer0_count >= 6000)  // 6000ms = 6��
    {
        Time_Flag = 1;       // ����6�뵽���־
        Timer0_count = 0;     // ���ü�����

    }
	
	if(++Key_Slow_Down == 20) Key_Slow_Down = 0;
}
void LCD9648_Init_Proc()
{
	switch(LCD_Init_Mode)
	{
		case 1:
			LCD9648_Init();
			LCD9648_Clear();
			
			LCD9648_Write16CnCHAR(0,0,"��");
			LCD9648_Write16CnCHAR(16,0,"��");
			LCD9648_Write16CnCHAR(32,0,"��");
		
			LCD9648_Write16EnCHAR(0,2,"202303103085");
			
			LCD9648_Write16CnCHAR(0,4,"��");
			LCD9648_Write16CnCHAR(16,4,"��");
			LCD9648_Write16EnCHAR(32,4,"23");
			LCD9648_Write16EnCHAR(48,4,"4");
			LCD9648_Write16CnCHAR(56,4,"��");
		break;
		
		case 2:
			LCD9648_Init();
			LCD9648_Clear();
			
			LCD9648_Write16CnCHAR(0,0,"��");
			LCD9648_Write16CnCHAR(16,0,"Я");
			LCD9648_Write16CnCHAR(32,0,"ʽ");
			LCD9648_Write16CnCHAR(48,0,"��");
			LCD9648_Write16CnCHAR(64,0,"��");
			LCD9648_Write16CnCHAR(80,0,"��");
			
			LCD9648_Write16EnCHAR(0,2,"25");//x=0,y=4,���ַ�
			LCD9648_Write16CnCHAR(16,2,"��");
			LCD9648_Write16EnCHAR(32,2,"07");
			LCD9648_Write16CnCHAR(48,2,"��");
			LCD9648_Write16EnCHAR(64,2,"11");
			LCD9648_Write16CnCHAR(80,2,"��");
		
			LCD9648_Write16CnCHAR(0,4,"��");
			LCD9648_Write16CnCHAR(16,4,"��");
			LCD9648_Write16EnCHAR(32,4,"1.");
			LCD9648_Write16EnCHAR(48,4,"23");
		break;
		
		case 3: //������ʾģʽ
            LCD9648_Init();
            LCD9648_Clear();
//            LCD9648_Write16CnCHAR(0,0,"��������ģʽ");
//            LCD9648_Write16CnCHAR(0,2,"�¶ȼ����...");
//			LCD9648_Write16EnCHAR(0,0,"#:");
//			LCD9648_Write16EnCHAR(0,2,"#:");
//			LCD9648_Write16EnCHAR(0,4,"#:");
//			LCD9648_Write16EnCHAR(0,4,"#:");
		
        break;
		
		default:
			
		break;
	}	
}

void LCD_Init_Test() //��ʼ״̬ʱ����⺯��
{
	if(Time_Flag)     //6��ʱ�䵽
	{
		Time_Flag = 0;
		Time_Flag_Count += 1;
		LCD_Init_Mode++; //�л�����һ��ģʽ
		if(LCD_Init_Mode <= 3)
		{
			LCD9648_Init_Proc(); //������ʾ
		}
		if(Time_Flag_Count == 2)
			LCD_Init_End_Flag ^= 1;
	}
	
}

void LCD_Proc()
{
    unsigned char temp_str[8];
    unsigned char i;
    unsigned char need_update = 0;
    
    if(!LCD_Init_End_Flag) return; // ��ʼ��δ���ʱ����ʾ
    
    // ����Ƿ���Ҫ������ʾ
    if(Last_LCD_Mode != LCD_Disp_Mode || Last_State_Index != State_Index)
    {
        need_update = 1;
        Last_LCD_Mode = LCD_Disp_Mode;
        Last_State_Index = State_Index;
        LCD9648_Clear(); // ģʽ�л�ʱ����
    }
    
    if(LCD_Disp_Mode == 0) // LCD��ʾ����
    {
        if(need_update)
        {
            // ��ʾ�����ʶ
            LCD9648_Write16EnCHAR(72,0,"[VIEW]");
            
            // ��ʾ�̶���ǩ��ֻ����Ҫ����ʱд�룩
            LCD9648_Write16EnCHAR(0,0,"NTC:");
            LCD9648_Write16EnCHAR(0,2,"SET:");  
            LCD9648_Write16EnCHAR(0,4,"VOL:");
        }
        
        // ��ʾ����ֵ��ʹ�ø�������ʽ��
        Float_To_String(State_Disp[0], temp_str, 'C');
        LCD9648_Write16EnCHAR(32,0,temp_str);
        
        Float_To_String(State_Disp[1], temp_str, 'C');
        LCD9648_Write16EnCHAR(32,2,temp_str);
        
        Float_To_String(State_Disp[2], temp_str, 'V');
        LCD9648_Write16EnCHAR(32,4,temp_str);
    }
    else if(LCD_Disp_Mode == 1) // LCD���ý���
    {
        if(need_update)
        {
            // ��ʾ�����ʶ
            LCD9648_Write16EnCHAR(72,0,"[SET]");
            
            // ��ʾѡ��ָʾ��
            for(i = 0; i < 3; i++) // ֻ��ʾ3��
            {
                if(i == State_Index)
                    LCD9648_Write16EnCHAR(0, i*2, ">");
                else
                    LCD9648_Write16EnCHAR(0, i*2, " ");
            }
            
            // ��ʾ��ǩ
            LCD9648_Write16EnCHAR(8,0,"NTC:");
            LCD9648_Write16EnCHAR(8,2,"SET:");
            LCD9648_Write16EnCHAR(8,4,"VOL:");
        }
        
        // ��ʾ����ֵ
        Float_To_String(State_Disp[0], temp_str, 'C');
        LCD9648_Write16EnCHAR(40,0,temp_str);
        
        Float_To_String(State_Disp[1], temp_str, 'C');
        LCD9648_Write16EnCHAR(40,2,temp_str);
        
        Float_To_String(State_Disp[2], temp_str, 'V');
        LCD9648_Write16EnCHAR(40,4,temp_str);
    }
}


void main()
{
    // ��ʼ����ʾ��ر���
    Last_LCD_Mode = 0xFF;
    Last_State_Index = 0xFF;
    
    LCD9648_Init_Proc(); // ��ʾ��ʼ����
    Timer0_Init();       // ������ʱ��
    
    while(1)
    {
        LCD_Init_Test();
        Key_Proc();
        LCD_Proc();
    }
}
